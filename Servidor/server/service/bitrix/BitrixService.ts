import axios from 'axios';
import Lead from '../../domain/crm/Lead';
import { Resposta } from '../../utils/Resposta';

interface BitrixConfig {
  baseUrl: string;
  userId: string;
  webhook: string;
}

interface BitrixPhone {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'MOBILE' | 'HOME' | 'OTHER';
}

interface BitrixEmail {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'HOME' | 'OTHER';
}

interface BitrixWeb {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'HOME' | 'OTHER';
}

interface BitrixLeadFields {
  TITLE?: string;
  NAME?: string;
  LAST_NAME?: string;
  COMPANY_TITLE?: string;
  STATUS_ID?: string;
  SOURCE_ID?: string;
  SOURCE_DESCRIPTION?: string;
  PHONE?: BitrixPhone[];
  EMAIL?: BitrixEmail[];
  WEB?: BitrixWeb[];
  COMMENTS?: string;
  ASSIGNED_BY_ID?: number;
  // Campos customizados podem ser adicionados com prefixo UF_
  [key: string]: any;
}

interface BitrixLeadPayload {
  fields: BitrixLeadFields;
  params?: {
    REGISTER_SONET_EVENT?: 'Y' | 'N'; // Notificar responsável
  };
}

export class BitrixService {
  private config: BitrixConfig;

  constructor(config: BitrixConfig) {
    this.config = config;
  }

  /**
   * Cria um lead no Bitrix24
   */
  async criarLead(lead: Lead): Promise<Resposta<number>> {
    try {
      const payload = this.converterLeadParaBitrix(lead);
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.add.json`;
      
      console.log('BITRIX: Criando lead:', JSON.stringify(payload, null, 2));
      console.log('BITRIX: URL:', url);

      const response = await axios.post(url, payload);
      
      if (response.data.error) {
        console.error('BITRIX: Erro ao criar lead:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      const leadId = response.data.result;
      console.log('BITRIX: Lead criado com sucesso. ID:', leadId);
      
      return Resposta.sucesso(leadId);
    } catch (error) {
      console.error('BITRIX: Erro na requisição:', error);
      return Resposta.erro(`Erro ao conectar com Bitrix: ${error.message}`);
    }
  }

  /**
   * Converte um Lead do nosso sistema para o formato do Bitrix
   */
  private converterLeadParaBitrix(lead: Lead): BitrixLeadPayload {
    // Extrair nome e sobrenome
    const partesNome = lead.nomeResponsavel.trim().split(' ');
    const nome = partesNome[0] || '';
    const sobrenome = partesNome.slice(1).join(' ') || '';

    // Montar comentários com informações extras
    const comentarios = this.montarComentarios(lead);

    // Determinar fonte baseada na origem
    const fonte = this.mapearOrigem(lead.origem);

    const fields: BitrixLeadFields = {
      TITLE: `${lead.empresa} - ${lead.nomeResponsavel}`,
      NAME: nome,
      LAST_NAME: sobrenome,
      COMPANY_TITLE: lead.empresa,
      STATUS_ID: this.mapearEtapaParaStatus(lead.etapa),
      SOURCE_ID: fonte.id,
      SOURCE_DESCRIPTION: fonte.descricao,
      COMMENTS: comentarios
    };

    // Adicionar telefone se disponível
    if (lead.telefone) {
      fields.PHONE = [{
        VALUE: this.formatarTelefone(lead.telefone),
        VALUE_TYPE: 'MOBILE'
      }];
    }

    // Adicionar email da empresa se disponível
    if (lead.crmEmpresa?.email) {
      fields.EMAIL = [{
        VALUE: lead.crmEmpresa.email,
        VALUE_TYPE: 'WORK'
      }];
    }

    // Adicionar links do Instagram e site
    const webs: BitrixWeb[] = [];
    
    if (lead.instagramHandle) {
      webs.push({
        VALUE: `https://instagram.com/${lead.instagramHandle}`,
        VALUE_TYPE: 'OTHER'
      });
    }

    if (lead.linkInsta) {
      webs.push({
        VALUE: lead.linkInsta,
        VALUE_TYPE: 'OTHER'
      });
    }

    if (lead.instagramData?.website) {
      webs.push({
        VALUE: lead.instagramData.website,
        VALUE_TYPE: 'WORK'
      });
    }

    if (webs.length > 0) {
      fields.WEB = webs;
    }

    return {
      fields,
      params: {
        REGISTER_SONET_EVENT: 'Y' // Notificar responsável
      }
    };
  }

  /**
   * Monta comentários com informações detalhadas do lead
   */
  private montarComentarios(lead: Lead): string {
    const comentarios: string[] = [];

    // Informações básicas
    comentarios.push(`Score: ${lead.score}%`);
    comentarios.push(`Segmento: ${lead.segmento || 'Não definido'}`);
    comentarios.push(`Etapa: ${lead.etapa}`);

    // Dados do Instagram
    if (lead.instagramData) {
      comentarios.push('\\n=== DADOS INSTAGRAM ===');
      if (lead.instagramData.followers) {
        comentarios.push(`Seguidores: ${lead.instagramData.followers.toLocaleString()}`);
      }
      if (lead.instagramData.following) {
        comentarios.push(`Seguindo: ${lead.instagramData.following.toLocaleString()}`);
      }
      if (lead.instagramData.accountType) {
        comentarios.push(`Tipo de conta: ${lead.instagramData.accountType}`);
      }
      if (lead.instagramData.businessCategory) {
        comentarios.push(`Categoria: ${lead.instagramData.businessCategory}`);
      }
      if (lead.bioInsta) {
        comentarios.push(`Bio: ${lead.bioInsta}`);
      }
    }

    // Observações
    if (lead.observacoes) {
      comentarios.push('\\n=== OBSERVAÇÕES DE VENDAS ===');
      comentarios.push(lead.observacoes);
    }

    // Notas do Instagram
    if (lead.notas) {
      comentarios.push('\\n=== DADOS EXTRAÍDOS ===');
      comentarios.push(lead.notas);
    }

    return comentarios.join('\\n');
  }

  /**
   * Mapeia origem do lead para fonte do Bitrix
   */
  private mapearOrigem(origem: string): { id: string; descricao: string } {
    const mapeamento = {
      'Instagram': { id: 'WEB', descricao: 'Lead gerado via Instagram' },
      'Site/Landing Page': { id: 'WEB', descricao: 'Lead do site/landing page' },
      'WhatsApp Direto': { id: 'OTHER', descricao: 'Contato direto via WhatsApp' },
      'Indicação': { id: 'PARTNER', descricao: 'Lead por indicação' },
      'Evento/Feira': { id: 'TRADE_SHOW', descricao: 'Lead de evento/feira' },
      'Outros': { id: 'OTHER', descricao: 'Outras fontes' }
    };

    return mapeamento[origem] || { id: 'OTHER', descricao: origem };
  }

  /**
   * Mapeia etapa do funil para status do Bitrix
   */
  private mapearEtapaParaStatus(etapa: string): string {
    const mapeamento = {
      'Prospecção': 'NEW',
      'Qualificação': 'IN_PROCESS', 
      'Objeção': 'IN_PROCESS',
      'Fechamento': 'PROCESSED',
      'Ganho': 'CONVERTED',
      'Perdido': 'JUNK'
    };

    return mapeamento[etapa] || 'NEW';
  }

  /**
   * Formata telefone para o padrão internacional
   */
  private formatarTelefone(telefone: string): string {
    const numeroLimpo = telefone.replace(/\D/g, '');
    
    if (numeroLimpo.startsWith('55')) {
      return `+${numeroLimpo}`;
    }
    
    return `+55${numeroLimpo}`;
  }

  /**
   * Busca um lead no Bitrix pelo ID
   */
  async buscarLead(leadId: number): Promise<Resposta<any>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.get.json?id=${leadId}`;
      
      const response = await axios.get(url);
      
      if (response.data.error) {
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      return Resposta.sucesso(response.data.result);
    } catch (error) {
      return Resposta.erro(`Erro ao buscar lead: ${error.message}`);
    }
  }

  /**
   * Atualiza um lead no Bitrix
   */
  async atualizarLead(leadId: number, campos: Partial<BitrixLeadFields>): Promise<Resposta<boolean>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.update.json`;
      
      const payload = {
        id: leadId,
        fields: campos
      };

      const response = await axios.post(url, payload);
      
      if (response.data.error) {
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      return Resposta.sucesso(true);
    } catch (error) {
      return Resposta.erro(`Erro ao atualizar lead: ${error.message}`);
    }
  }
}

/**
 * Factory para criar instância do BitrixService com configuração padrão
 */
export class BitrixServiceFactory {
  static criarInstancia(): BitrixService {
    const config: BitrixConfig = {
      baseUrl: 'https://b24-chlbsw.bitrix24.com.br',
      userId: '1',
      webhook: '19i08a5m1x8am1f6'
    };

    return new BitrixService(config);
  }

  static criarInstanciaPersonalizada(baseUrl: string, userId: string, webhook: string): BitrixService {
    const config: BitrixConfig = {
      baseUrl,
      userId, 
      webhook
    };

    return new BitrixService(config);
  }
}