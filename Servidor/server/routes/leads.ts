import {Router} from 'express';
import MapeadorDeLead from '../mapeadores/MapeadorDeLead';
import {Resposta} from '../utils/Resposta';
import Lead, { OrigemLead, InstagramData } from '../domain/crm/Lead';
import { CrmEmpresa } from '../domain/crm/CrmEmpresa';
import MapeadorDeCrmEmpresa from '../mapeadores/MapeadorDeCrmEmpresa';
import { IgApiClient } from 'instagram-private-api';
import { sample } from 'lodash';
import { ChatGPTService } from '../service/ia/ChatGPTService';

const router: Router = Router();
const axios = require('axios');


// Rota dadosig que recebe dados do Instagram prontos
router.post('/dadosig', async (req: any, res) => {
  try {
    const { instagramData, crmEmpresaId } = req.body;

    if (!instagramData || !instagramData.data || !instagramData.data.user) {
      return res.json(Resposta.erro('Dados do Instagram são obrigatórios'));
    }
    // crmEmpresaId agora é opcional – se não vier será criado/selecionado automaticamente

    const user = instagramData.data.user;
    const username = user.username;

    console.log('Processando dados do Instagram para:', username);

    // Debug dos bio_links
    if (user?.bio_links) {
      console.log('Bio Links encontrados:', JSON.stringify(user.bio_links, null, 2));
    }
    if (!user) {
      console.error('Dados do usuário não encontrados nos dados do Instagram');
      return res.json(Resposta.erro('Dados do usuário do Instagram não encontrados'));
    }

    // Obter ou criar empresa
    let crmEmpresaIdNumeric: number;
    let empresaObj: CrmEmpresa;

    if (crmEmpresaId) {
      crmEmpresaIdNumeric = parseInt(crmEmpresaId as string, 10);
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();
      empresaObj = await mapeadorCrmEmpresa.selecioneSync({ id: crmEmpresaIdNumeric });
    } else {
      const nomeEmpresa = user.full_name || user.username;
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

      // Tenta encontrar empresa existente pelo nome (primeira ocorrência)
      const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: nomeEmpresa });

      if (existente && existente.id) {
        crmEmpresaIdNumeric = existente.id;
        empresaObj = existente;
      } else {
        const novaEmpresa = new CrmEmpresa(nomeEmpresa);
        // Preencher dados básicos quando disponíveis
        if (user.business_phone_number) novaEmpresa.telefone = user.business_phone_number;
        if (user.business_email) novaEmpresa.email = user.business_email;
        if (user.business_address_json) novaEmpresa.endereco = JSON.stringify(user.business_address_json);

        const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);

        crmEmpresaIdNumeric = criada.id;
        empresaObj = novaEmpresa;
      }
    }

    // Montar objeto instagramData conforme nosso domínio
    const instagramDataFormatted: InstagramData = {
      bio: user.biography,
      followers: user.edge_followed_by?.count,
      following: user.edge_follow?.count,
      accountType: (user.is_business_account ? 'Business' : 'Pessoal') as 'Business' | 'Pessoal',
      businessCategory: user.business_category_name || user.category_name,
      location: user.business_address_json ? JSON.stringify(user.business_address_json) : undefined,
      website: user.external_url
    };

    // Processar bio_links se existir
    let observacoesBioLinks = '';
    if (user.bio_links && Array.isArray(user.bio_links) && user.bio_links.length > 0) {
      const links = user.bio_links.map((link: any, index: number) => {
        if (typeof link === 'string') {
          return `${index + 1}. ${link}`;
        } else if (link && link.url) {
          const titulo = link.title || link.text || 'Link';
          return `${index + 1}. ${titulo}: ${link.url}`;
        }
        return `${index + 1}. ${JSON.stringify(link)}`;
      });
      observacoesBioLinks = `Bio Links do Instagram:\n${links.join('\n')}`;
    }

    // Criar Lead parcialmente preenchido
    const lead = new Lead(
      crmEmpresaIdNumeric,
      user.full_name || user.username,
      user.full_name || user.username,
      user.business_phone_number || '',
      user.username,
      user.biography || undefined,
      OrigemLead.Instagram
    );
    lead.instagramData = instagramDataFormatted;
    lead.linkInsta = user.external_url || null; // Link da bio do Instagram
    lead.notas = observacoesBioLinks || undefined; // Adicionar bio_links nas observações
    if (empresaObj) lead.crmEmpresa = empresaObj;

    res.json(Resposta.sucesso(lead));
  } catch (err) {
    console.error('Erro ao processar dados do Instagram:', err);
    res.json(Resposta.erro('Erro ao processar dados do Instagram: ' + err.message));
  }
});

// Rota dadosig2 que recebe texto bruto e extrai dados usando IA
router.post('/dadosig2', async (req: any, res) => {
  try {
    const { texto, crmEmpresaId } = req.body;

    if (!texto || typeof texto !== 'string') {
      return res.json(Resposta.erro('Texto é obrigatório'));
    }

    console.log('Processando texto do Instagram com IA...');

    // Prompt para extrair dados do Instagram e criar objeto Lead estruturado
    const prompt = `
Você é um especialista em análise de perfis do Instagram para geração de leads.
Analise o texto fornecido e extraia as informações para criar um objeto Lead completo.

Retorne um JSON com a seguinte estrutura EXATA:

{
  "crmEmpresa": {
    "nome": "nome_da_empresa_extraido",
    "cnpj": null,
    "telefone": "telefone_apenas_numeros_ou_null",
    "email": null,
    "endereco": "endereco_completo_ou_null",
    "ativa": true
  },
  "lead": {
    "nomeResponsavel": "nome_do_responsavel_ou_nome_empresa",
    "empresa": "nome_da_empresa",
    "telefone": "telefone_apenas_numeros_ou_vazio",
    "instagramHandle": "username_sem_@",
    "bioInsta": "biografia_ou_descricao_do_perfil",
    "origem": "Instagram",
    "etapa": "Prospecção",
    "score": 0,
    "instagramData": {
      "bio": "biografia_ou_descricao_do_perfil",
      "followers": numero_de_seguidores_ou_null,
      "following": numero_de_seguindo_ou_null,
      "accountType": "Business",
      "businessCategory": "categoria_do_negocio_ou_null",
      "location": "endereco_completo_ou_null",
      "website": "website_se_houver_ou_null"
    },
    "linkInsta": "website_se_houver_ou_null",
    "notas": "TEXTO_COMPLETO_DO_PERFIL_MAIS_informacoes_extras"
  }
}

REGRAS IMPORTANTES:
- Se não encontrar uma informação, use null (não string "null")
- Para telefone, extraia apenas números (ex: "***********")
- Para seguidores/seguindo/publicações, use números inteiros ou null
- Para username, remova @ se houver
- Para notas, SEMPRE inclua o TEXTO COMPLETO DO PERFIL no início, seguido de informações extras organizadas
- Mantenha a estrutura JSON exata conforme especificado
- Use "Business" para accountType sempre que for um perfil comercial

FORMATO DAS NOTAS:
"TEXTO ORIGINAL DO PERFIL:
[texto_completo_aqui]

INFORMAÇÕES EXTRAÍDAS:
- Horário: [horario_se_houver]
- Serviços: [servicos_se_houver]
- WhatsApp: [whatsapp_se_houver]
- Publicações: [numero_publicacoes_se_houver]"

Texto para análise:
${texto}
`;

    // Chamar ChatGPT para extrair dados
    const chatGPTService = new ChatGPTService();
    const resultado: any = await chatGPTService.chameOpenAIChat(
      'sistema',
      'extrair_dados_instagram',
      prompt,
      texto,
      [],
      0.3,
      '[dadosig2]',
      { type: "json_object" }
    );

    if (!resultado || !resultado.text) {
      console.error('Erro na resposta da IA:', resultado);
      return res.json(Resposta.erro('Erro ao processar dados com IA'));
    }

    // Parse da resposta JSON
    let dadosEstruturados;
    try {
      dadosEstruturados = JSON.parse(resultado.text);
    } catch (parseError) {
      console.error('Erro ao fazer parse da resposta da IA:', parseError);
      return res.json(Resposta.erro('Erro ao interpretar resposta da IA'));
    }

    console.log('Dados estruturados pela IA:', dadosEstruturados);

    // Validar estrutura da resposta
    if (!dadosEstruturados.crmEmpresa || !dadosEstruturados.lead) {
      console.error('Estrutura inválida na resposta da IA:', dadosEstruturados);
      return res.json(Resposta.erro('Estrutura de dados inválida retornada pela IA'));
    }

    // Obter ou criar empresa
    let crmEmpresaIdNumeric: number;
    let empresaObj: CrmEmpresa;

    if (crmEmpresaId) {
      crmEmpresaIdNumeric = parseInt(crmEmpresaId as string, 10);
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();
      empresaObj = await mapeadorCrmEmpresa.selecioneSync({ id: crmEmpresaIdNumeric });
    } else {
      const dadosEmpresa = dadosEstruturados.crmEmpresa;
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

      // Tenta encontrar empresa existente pelo nome
      const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: dadosEmpresa.nome });

      if (existente && existente.id) {
        crmEmpresaIdNumeric = existente.id;
        empresaObj = existente;
      } else {
        // Criar nova empresa com dados extraídos pela IA
        const novaEmpresa = new CrmEmpresa(dadosEmpresa.nome);
        novaEmpresa.telefone = dadosEmpresa.telefone;
        novaEmpresa.endereco = dadosEmpresa.endereco;
        novaEmpresa.email = dadosEmpresa.email;
        novaEmpresa.cnpj = dadosEmpresa.cnpj;
        novaEmpresa.ativa = dadosEmpresa.ativa;

        const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
        crmEmpresaIdNumeric = criada.id;
        empresaObj = novaEmpresa;
        empresaObj.id = criada.id;
      }
    }

    // Criar Lead com dados estruturados pela IA
    const dadosLead = dadosEstruturados.lead;

    const lead = new Lead(
      crmEmpresaIdNumeric,
      dadosLead.nomeResponsavel,
      dadosLead.empresa,
      dadosLead.telefone || '',
      dadosLead.instagramHandle,
      dadosLead.bioInsta,
      OrigemLead.Instagram
    );

    // Aplicar dados estruturados do Lead
    lead.etapa = dadosLead.etapa as any;
    lead.score = dadosLead.score;
    lead.instagramData = dadosLead.instagramData;
    lead.linkInsta = dadosLead.linkInsta;
    lead.notas = dadosLead.notas;

    // Vincular empresa
    lead.crmEmpresa = empresaObj;

    res.json(Resposta.sucesso(lead));

  } catch (err) {
    console.error('Erro ao processar dados do Instagram com IA:', err);
    res.json(Resposta.erro('Erro ao processar dados do Instagram com IA: ' + err.message));
  }
});

// Listagem com filtros e paginação
router.get('/', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const params = {
      inicio: req.query.inicio ? parseInt(req.query.inicio) : 0,
      total: req.query.total ? parseInt(req.query.total) : 20,
      texto: req.query.texto || null,
      etapa: req.query.etapa || null,
      crmEmpresaId: req.query.crmEmpresaId || null
    };

    const dados = await mapeador.listeAsync(params);
    const total = await mapeador.selecioneTotal(params);
    res.json(Resposta.sucesso({ data: dados, total }));
  } catch (err) {
    console.error('Erro ao listar leads', err);
    res.json(Resposta.erro('Erro ao listar leads'));
  }
});

// Buscar lead por username do Instagram
router.get('/porUsername/:username', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const username = req.params.username.replace('@', '');
    const lead = await mapeador.selecioneSync({ instagramHandle: username });
    if (!lead) return res.json(Resposta.erro('Lead não encontrado'));
    res.json(Resposta.sucesso(lead));
  } catch (err) {
    console.error('Erro ao buscar lead por username', err);
    res.json(Resposta.erro('Erro ao buscar lead por username'));
  }
});

// Selecionar um lead
router.get('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const lead = await mapeador.selecioneSync({ id: req.params.id });
    if (!lead) return res.json(Resposta.erro('Lead não encontrado'));
    res.json(Resposta.sucesso(lead));
  } catch (err) {
    console.error('Erro ao obter lead', err);
    res.json(Resposta.erro('Erro ao obter lead'));
  }
});

// Inserir lead
router.post('/', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const obj = req.body;
    console.log('Dados recebidos para inserir lead:', JSON.stringify(obj, null, 2));

    // Se não tiver crmEmpresaId, usar uma empresa padrão ou criar uma nova
    if (!obj.crmEmpresaId) {
      // Usar empresa padrão ou criar uma baseada no nome da empresa do lead
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

      if (obj.empresa) {
        // Tenta encontrar empresa existente pelo nome
        const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: obj.empresa });

        if (existente && existente.id) {
          obj.crmEmpresaId = existente.id;
        } else {
          // Criar nova empresa
          const novaEmpresa = new CrmEmpresa(obj.empresa);
          if (obj.telefone) novaEmpresa.telefone = obj.telefone;

          const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
          obj.crmEmpresaId = criada.id;
        }
      } else {
        // Criar empresa com nome genérico baseado no responsável
        const nomeEmpresa = `${obj.nomeResponsavel || 'Lead'} - Instagram`;
        const novaEmpresa = new CrmEmpresa(nomeEmpresa);
        if (obj.telefone) novaEmpresa.telefone = obj.telefone;

        const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
        obj.crmEmpresaId = criada.id;
      }
    }

    const novo = await mapeador.insiraSync(obj);
    res.json(Resposta.sucesso(novo));
  } catch (err) {
    console.error('Erro ao inserir lead', err);
    res.json(Resposta.erro('Erro ao inserir lead: ' + err.message));
  }
});

// Atualizar lead
router.put('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const obj = req.body;
    obj.id = req.params.id;
    const atualizado = await mapeador.atualizeSync(obj);
    res.json(Resposta.sucesso(atualizado));
  } catch (err) {
    console.error('Erro ao atualizar lead', err);
    res.json(Resposta.erro('Erro ao atualizar lead'));
  }
});

// Remover lead
router.delete('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    await mapeador.removaAsync({ id: req.params.id });
    res.json(Resposta.sucesso({}));
  } catch (err) {
    console.error('Erro ao remover lead', err);
    res.json(Resposta.erro('Erro ao remover lead'));
  }
});

export const LeadsController: Router = router;
