import * as moment from "moment";
import {Fatura} from "./Fatura";
import {EnumStatusFatura} from "../../lib/emun/EnumStatusFatura";
import {Endereco} from "../delivery/Endereco";
import {Empresa} from "../Empresa";
import {Ambiente} from "../../service/Ambiente";

export class NotaFiscalDeServico {
  public id: number
  public dataEmissao: Date;
  public aprovada = false;
  public xml: string;
  public valorServicos: number;
  public discriminacao: string;
  public tipoDocumentoTomador: string;
  public cnpjTomador: string;
  public cpfTomador: string;
  public nomeTomador: string;
  public enderecoTomador: Endereco;
  public numeroDaNota: number;
  public codigoVerificacao: string;
  public xmlResposta: string;
  public empresaTomador: Empresa;
  public link: string;

  constructor(public fatura: Fatura, public numeroRps: number) {
    if(!fatura) return

    this.empresaTomador = fatura.empresa
    this.dataEmissao = new Date()
    this.calculeDados();
  }

  private calculeDados() {
    let itensDiscriminacao: any[] = []

    let dados = this.fatura.getDados()
    dados.lancamentos.forEach((lancamento: any) => {
      itensDiscriminacao.push(lancamento.description.normalize("NFD").replace(/\p{Diacritic}/gu, ""))
    })

    this.discriminacao = itensDiscriminacao.join(" / ")

    let numero = dados.total

    this.valorServicos =
      Number.parseFloat((numero.indexOf("BRL", 0) > 0 ?  numero.replace(",", "") :
        numero.replace("R$ ", "").replace(".", "").replace(",", ".")))

  }

  private removaAcentos(texto: string): string {
    return texto.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace("'", " ").trim()

  }

  gereXml(regerarXML = true): string {
    if(!regerarXML && this.xml)
      return this.xml

    if(this.valideCampos().length > 0)
      return null

    if(!(this.fatura.status === EnumStatusFatura.Paga))
      return null


    this.dataEmissao = new Date()
    let data = moment(this.dataEmissao);



    this.tipoDocumentoTomador  = this.fatura.contrato.empresa.cnpj ? 'cnpj' : (this.fatura.contrato.empresa.responsavel ? 'cpf' : null)

    if(!this.tipoDocumentoTomador) return null; // não é possível emitir nota sem documento do tomador

    if(this.tipoDocumentoTomador === 'cnpj')
      this.cnpjTomador = this.fatura.contrato.empresa.cnpj
    else
      this.cpfTomador = this.fatura.contrato.empresa.responsavel.cpf

    this.nomeTomador = this.fatura.contrato.empresa.nome

    if(!this.fatura.contrato.empresa.enderecoCompleto)
      return null

    this.enderecoTomador =  new Endereco(null, null, null, null, null, null,
      null, null)

    Object.assign(this.enderecoTomador, this.fatura.contrato.empresa.enderecoCompleto)

    if((this.enderecoTomador as any).estado)
      delete (this.enderecoTomador as any).estado

    this.enderecoTomador.id = null;

    if(!this.enderecoTomador.cidade.codigoNfse)
      return null;

    if(!this.enderecoTomador.numero && !this.enderecoTomador.complemento)
      return null;

    this.calculeDados()

    this.xml = `<GerarNfseEnvio xmlns="http://nfse.goiania.go.gov.br/xsd/nfse_gyn_v02.xsd">
  <Rps>
    <InfDeclaracaoPrestacaoServico>
      <Rps>
        <IdentificacaoRps>
          <Numero>${this.numeroRps}</Numero>
          <Serie>${Ambiente.Instance.producao ? "UNICA" : "TESTE"}</Serie>
          <Tipo>1</Tipo>
        </IdentificacaoRps>
        <DataEmissao>${data.format("YYYY-MM-DD")}T00:00:00</DataEmissao>
        <Status>1</Status>
      </Rps>
      <Servico>
        <Valores>
          <ValorServicos>${this.valorServicos.toFixed(2)}</ValorServicos>
          <ValorPis>0.00</ValorPis>
          <ValorCofins>0.00</ValorCofins>
          <ValorInss>0.00</ValorInss>
          <ValorCsll>0.00</ValorCsll>
        </Valores>
        <CodigoTributacaoMunicipio>631940000</CodigoTributacaoMunicipio>
        <Discriminacao>${this.removaAcentos(this.discriminacao)}</Discriminacao>
        <CodigoMunicipio>25300</CodigoMunicipio>
      </Servico>
      <Prestador>
        <CpfCnpj>
          <Cnpj>08150325000162</Cnpj>
        </CpfCnpj>
        <InscricaoMunicipal>2714061</InscricaoMunicipal>
      </Prestador>
      <Tomador>
        <IdentificacaoTomador>
          <CpfCnpj>
            ${this.tipoDocumentoTomador === 'cnpj' ?
              '<Cnpj>' + this.cnpjTomador  + '</Cnpj>' :
              '<Cpf>' + this.cpfTomador + '</Cpf>'
             }
          </CpfCnpj>
        </IdentificacaoTomador>
        <RazaoSocial>${this.removaAcentos(this.nomeTomador)}</RazaoSocial>
        <Endereco>
          <Endereco>${this.removaAcentos(this.enderecoTomador.logradouro)}</Endereco>
          ${
            this.enderecoTomador.numero ?
         '<Numero>' + this.enderecoTomador.numero  + '</Numero>'
              :
              ''
          }
           ${
              this.enderecoTomador.complemento ?
                '<Complemento>' + this.removaAcentos(this.enderecoTomador.complemento) + '</Complemento>'
              : ''
            }
          <Bairro>${this.removaAcentos(this.enderecoTomador.bairro)}</Bairro>
          <CodigoMunicipio>${this.enderecoTomador.cidade.codigoNfse}</CodigoMunicipio>
          <Uf>GO</Uf>
        </Endereco>
      </Tomador>
    </InfDeclaracaoPrestacaoServico>
  </Rps>
</GerarNfseEnvio>
    `

    return this.xml
  }

  obtenhaLinkNota(){
    if(!this.aprovada) return null;

    return `https://www11.goiania.go.gov.br/sistemas/snfse/asp/snfse00200w0.asp?inscricao=2714061&nota=${this.numeroDaNota}&verificador=${this.codigoVerificacao}`
  }

  public valideCampos(): any[] {
    let mensagens = []

    if(!(this.fatura.status === EnumStatusFatura.Paga))
      mensagens.push("A fatura precisa estar paga para que a nfse possa ser emitida")

    if(!this.fatura.contrato.empresa.cnpj && !this.fatura.contrato.empresa.responsavel.cpf)
      mensagens.push("É necessário informar o cnpj da empresa ou o cpf do responsável")

    if(!this.fatura.contrato.empresa.enderecoCompleto)
      mensagens.push("O endereço da empresa tomadora não foi informado")
    else {
      if(!this.fatura.contrato.empresa.enderecoCompleto.cidade.codigoNfse)
        mensagens.push("Não foi encontrado Código para Emissão de NFse para a cidade " +
          this.fatura.contrato.empresa.enderecoCompleto.cidade.nome + " - "
          + this.fatura.contrato.empresa.enderecoCompleto.cidade.estado.sigla);

      if(!this.fatura.contrato.empresa.enderecoCompleto.numero &&
        !this.fatura.contrato.empresa.enderecoCompleto.complemento)
        mensagens.push("É preciso informar o numero ou o complemento do endereço da empresa tomadora")
    }

    return mensagens;
  }
}
