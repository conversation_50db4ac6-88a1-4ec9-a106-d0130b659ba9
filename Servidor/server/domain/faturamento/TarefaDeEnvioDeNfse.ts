import {NotaFiscalDeServico} from "./NotaFiscalDeServico";
import {EnumStatusTarefaEnvioNfse} from "../../lib/emun/EnumStatusTarefaEnvioNfse";
import {Fatura} from "./Fatura";
import {MapeadorDeNotaFiscalDeServico} from "../../mapeadores/MapeadorDeNotaFiscalDeServico";
import {Resposta} from "../../utils/Resposta";
import {MapeadorDeTarefaDeEnvioDeNfse} from "../../mapeadores/MapeadorDeTarefaDeEnvioDeNfse";
import {ExecutorAsync} from "../../utils/ExecutorAsync";
import {AssinadorDeXml} from "../../utils/nfse/AssinadorDeXml";
import {InvocadorSoapNfseGoiania} from "../../utils/nfse/InvocadorSoapNfseGoiania";
import {MapeadorDeEndereco} from "../../mapeadores/MapeadorDeEndereco";

const AsyncLock = require('async-lock');
const lock = new AsyncLock({timeout: 60000});

export class TarefaDeEnvioDeNfse {
  static enderecoNfse = 'https://nfse.goiania.go.gov.br/ws/nfse.asmx'

  public id: number;
  public status: EnumStatusTarefaEnvioNfse;
  public mensagem: string;

  public tentativas = 0;

  constructor(public nota: NotaFiscalDeServico) {
    this.status = EnumStatusTarefaEnvioNfse.Nova;
  }

  static crieTarefaOuObtenha(fatura: Fatura): Promise<Resposta<any>> {
    return new Promise<Resposta<any>>((resolve) => {
      TarefaDeEnvioDeNfse.crieTarefaDeEnvioDeNota(fatura).then((resposta: Resposta<any>) => {
        if(resposta.sucesso)
          return resolve(resposta)

        let mapeadorDeTarefa = new MapeadorDeTarefaDeEnvioDeNfse()

        mapeadorDeTarefa.selecioneSync({fatura: fatura}).then((tarefa: TarefaDeEnvioDeNfse) => {
          if(!tarefa)
            return resolve(resposta)

          if(tarefa.status === EnumStatusTarefaEnvioNfse.EmProcessamento)
            return resolve(Resposta.erro("Existe uma tarefa de envio em processamento para a fatura, com o id " + tarefa.id))

          if(tarefa.status === EnumStatusTarefaEnvioNfse.Concluida)
            return resolve(Resposta.erro( "Já foi enviada uma nota fiscal para essa fatura."))

          resolve(Resposta.sucesso(tarefa))
        })

      })

    })
  }

  static crieTarefaDeEnvioDeNota(fatura: Fatura): Promise<Resposta<any>> {
    if(!fatura.estaPaga()) {
      return Promise.resolve(Resposta.erro("A fatura " + fatura.id + " precisa estar paga para que a nota seja emitida"))
    }

    return new Promise<Resposta<any>>((resolve) => {
      let mapeadorDeNfse = new MapeadorDeNotaFiscalDeServico()

      lock.acquire("ultimoRps", (done: any) => {//precisa de lock para evitar gerar 2 notas para o mesmo RPS
        mapeadorDeNfse.listeAsync({fatura: fatura}).then((nfsesExistentes) => {
          if(nfsesExistentes && nfsesExistentes.length > 0) {
            done();
            return resolve(Resposta.erro("A nota " +  nfsesExistentes[0].id + " já foi criada para a fatura " + fatura.id))
          } //já existe uma nota para essa fatura, não criar outra


          mapeadorDeNfse.obtenhaUltimoRps().then((ultimoRps: number) => {
            if(!ultimoRps) ultimoRps = 16151;

            let novaNota = new NotaFiscalDeServico(fatura, ultimoRps + 1)

            mapeadorDeNfse.insiraGraph(novaNota).then(() => {
              let tarefaDeEnvio = new TarefaDeEnvioDeNfse(novaNota)
              done();
              new MapeadorDeTarefaDeEnvioDeNfse().insiraGraph(tarefaDeEnvio).then(() => {

                resolve(Resposta.sucesso(tarefaDeEnvio))
              }).catch((reason: any) => {
                console.log("Houve um erro ao inserir a tarefa de envio da nfse " + novaNota.id)
                console.log(reason)
                resolve(Resposta.erro(reason))
              })
            }).catch((reason: any) => {
              console.log("Houve um erro ao inserir a nfse da fatura " + fatura.id)
              console.log(reason)
              done();
              resolve(Resposta.erro(reason))
            })

          }).catch((reason: any) => {
            console.log("Houve um erro ao obter a ultima rps para a fatura " + fatura.id)
            console.log(reason)
            done();
            resolve(Resposta.erro(reason))

          })



        }).catch((reason: any) => {
          console.log("Houve um erro ao consultar a nota da fatura " + fatura.id)
          console.log(reason)
          done();
          resolve(Resposta.erro(reason))
        })


      }).catch((err: any) => {
        console.log("Houve um erro no tratamento do lock")
        console.log(err.message)
        resolve(Resposta.erro(err.message))
      })



    })
  }

  execute(envie: boolean = true) {
    if(this.status === EnumStatusTarefaEnvioNfse.Concluida || this.status === EnumStatusTarefaEnvioNfse.EmProcessamento)
     return Promise.resolve(Resposta.erro("Esta tarefa já está em processamento"));

    let mapeadorDeTarefa = new MapeadorDeTarefaDeEnvioDeNfse()

    return new Promise((resolve, reject) => {
      mapeadorDeTarefa.transacao(async (conexao: any, commit: any) => {
        this.status = EnumStatusTarefaEnvioNfse.EmProcessamento;

        mapeadorDeTarefa.atualizeStatus(this).then(() => {
          commit( () => {
            ExecutorAsync.execute( async (cb: Function) => {
              console.log('Realizando o envio da nfse');
              require('domain').active.contexto.idEmpresa = -1;

              let nota = this.nota

              let xml = nota.gereXml(true)

              if(!xml) {
                let mensagens = nota.valideCampos()

                let stringMensagens = mensagens.join(" - ");

                this.mensagem = stringMensagens
                this.status = EnumStatusTarefaEnvioNfse.Erro
                mapeadorDeTarefa.atualizeStatus(this).then(() => {
                  resolve(Resposta.erro(stringMensagens))
                  cb()
                })

              } else {
                let xmlAssinado = AssinadorDeXml.assine(xml)

                nota.xml = xmlAssinado

                let mapeadorDeNota = new MapeadorDeNotaFiscalDeServico()

                mapeadorDeNota.transacao((conexao4: any, commit4: any) => {
                  (new MapeadorDeEndereco()).insiraGraph(nota.enderecoTomador).then(() => {
                    mapeadorDeNota.atualizeSync(nota).then(() => {
                      commit4(() => {

                        let invocador = new InvocadorSoapNfseGoiania(TarefaDeEnvioDeNfse.enderecoNfse)

                        if(envie)
                          invocador.invoqueWebService(xmlAssinado).then((resposta: any) => {
                          if(!resposta.sucesso) {
                            this.status = EnumStatusTarefaEnvioNfse.Erro
                            this.mensagem = resposta.mensagem ? resposta.mensagem : resposta.resposta;

                            mapeadorDeTarefa.atualizeStatus(this).then(() => {
                              resolve(Resposta.erro(this.mensagem))
                              cb()
                            });


                          }
                          else {
                            this.nota.aprovada = true;
                            this.nota.numeroDaNota = resposta.resumo.numero;
                            this.nota.codigoVerificacao = resposta.resumo.codigoVerificacao;
                            this.nota.xmlResposta =  resposta.xmlNfseProcessado

                            mapeadorDeNota.transacao((conexao3: any, commit3: any) => {
                              mapeadorDeNota.foiAprovada(nota).then(() => {
                                this.status = EnumStatusTarefaEnvioNfse.Concluida
                                this.mensagem = "A Nfse " + this.nota.numeroDaNota +  " para a rps " + this.nota.numeroRps +
                                  " foi aprovada."

                                mapeadorDeTarefa.atualizeStatus(this).then(() => {
                                  commit3(() => {
                                    resolve(Resposta.sucesso(this.nota))
                                    cb()
                                  })
                                })
                              })
                            })
                          }
                        })
                        else {
                          this.status = EnumStatusTarefaEnvioNfse.Nova
                          //a nota, neste caso, é criada porém não é enviada, é apenas salva
                          mapeadorDeTarefa.atualizeStatus(this).then(() => {
                            resolve(Resposta.sucesso(this.nota))
                            cb()
                          })

                        }
                      })
                    })

                  })


                })
              }



            }, (erro: Error) => {
              if(erro){
                resolve(Resposta.erro(erro.message))
              }
              //done(erro);
            }, 0);
          });
        })

      })

    })

  }
}
