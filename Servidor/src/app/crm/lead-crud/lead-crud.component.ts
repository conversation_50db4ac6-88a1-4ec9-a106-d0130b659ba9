import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LeadService } from '../services/lead.service';
import { CrmEmpresaService } from '../services/crm-empresa.service';

@Component({
  selector: 'app-lead-crud',
  templateUrl: './lead-crud.component.html',
  styleUrls: ['./lead-crud.component.scss']
})
export class LeadCrudComponent implements OnInit {
  leads: any[] = [];
  leadsFiltrados: any[] = [];
  leadSelecionado: any = {};
  carregando = false;
  modoEdicao = false;
  
  // Lista de empresas CRM para seleção
  crmEmpresas: any[] = [];

  // Controle de filtros
  mostrarFiltros = false;
  filtroEtapa = '';
  filtroOrigem = '';
  filtroTexto = '';
  filtroPendencias = false;
  // Controle de busca Instagram
  buscandoInstagram = false;
  instagramUsername = '';

  // Opções para dropdowns baseadas nos enums do Lead.ts
  etapas = [
    { valor: 'Prospecção', texto: 'Prospecção' },
    { valor: 'Qualificação', texto: 'Qualificação' }, 
    { valor: 'Objeção', texto: 'Objeção' },
    { valor: 'Fechamento', texto: 'Fechamento' },
    { valor: 'Ganho', texto: 'Ganho' },
    { valor: 'Perdido', texto: 'Perdido' }
  ];

  origens = [
    { valor: 'Instagram', texto: 'Instagram' },
    { valor: 'Site/Landing Page', texto: 'Site/Landing Page' },
    { valor: 'WhatsApp Direto', texto: 'WhatsApp Direto' },
    { valor: 'Indicação', texto: 'Indicação' },
    { valor: 'Evento/Feira', texto: 'Evento/Feira' },
    { valor: 'Outros', texto: 'Outros' }
  ];

  segmentos = [
    { valor: 'Restaurante', texto: 'Restaurante' },
    { valor: 'Pizzaria', texto: 'Pizzaria' },
    { valor: 'Lanchonete', texto: 'Lanchonete' },
    { valor: 'Hamburgueria', texto: 'Hamburgueria' },
    { valor: 'Confeitaria', texto: 'Confeitaria/Doceria' },
    { valor: 'Bar', texto: 'Bar/Boteco' },
    { valor: 'Food Truck', texto: 'Food Truck' },
    { valor: 'Outros', texto: 'Outros' }
  ];

  constructor(
    private leadService: LeadService,
    private crmEmpresaService: CrmEmpresaService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Primeiro carrega as empresas, depois os leads
    this.carregarEmpresas().then(() => {
      this.listar();
    });
  }

  carregarEmpresas(): Promise<void> {
    return this.crmEmpresaService.liste({ ativa: true }).then((resp) => {
      this.crmEmpresas = resp.data?.data || resp.data || [];
      console.log('Empresas CRM carregadas:', this.crmEmpresas);
    }).catch((erro) => {
      console.error('Erro ao carregar empresas CRM:', erro);
      alert('Erro ao carregar empresas CRM. Verifique se há empresas cadastradas em /crm/empresas');
    });
  }

  listar(): void {
    this.carregando = true;
    this.leadService.liste({ inicio: 0, total: 100 }).then((resp) => {
      this.leads = resp.data?.data || resp.data || [];
      console.log('Leads carregados:', this.leads);
      console.log('CRM Empresas disponíveis:', this.crmEmpresas);
      this.aplicarFiltros();
      this.carregando = false;
    }).catch(() => this.carregando = false);
  }

  // Controle de filtros
  toggleFiltros(): void {
    this.mostrarFiltros = !this.mostrarFiltros;
  }

  aplicarFiltros(): void {
    this.leadsFiltrados = this.leads.filter(lead => {
      // Filtro por etapa
      if (this.filtroEtapa && lead.etapa !== this.filtroEtapa) {
        return false;
      }
      
      // Filtro por origem
      if (this.filtroOrigem && lead.origem !== this.filtroOrigem) {
        return false;
      }
      
      // Filtro por texto
      if (this.filtroTexto) {
        const texto = this.filtroTexto.toLowerCase();
        const contemTexto = 
          lead.nomeResponsavel?.toLowerCase().includes(texto) ||
          lead.telefone?.toLowerCase().includes(texto) ||
          lead.instagramHandle?.toLowerCase().includes(texto);
        
        if (!contemTexto) {
          return false;
        }
      }
      
      // Filtro por pendências
      if (this.filtroPendencias && !this.isAtrasado(lead)) {
        return false;
      }
      
      return true;
    });
  }

  novo(): void {
    this.leadSelecionado = {
      id: null,
      crmEmpresa: null,
      nomeResponsavel: '',
      empresa: '',
      telefone: '',
      instagramHandle: '',
      linkInsta: '',
      bioInsta: '',
      etapa: 'Prospecção',
      origem: 'Instagram',
      score: 0,
      valorPotencial: 0,
      segmento: '',
      notas: ''
    };
    this.instagramUsername = '';
    this.modoEdicao = true;
  }

  editar(lead: any): void {
    console.log('Editando lead:', lead);
    this.leadSelecionado = { ...lead };
    
    // Garantir que os campos numéricos estejam corretos para a edição
    if (this.leadSelecionado.score) {
      this.leadSelecionado.score = parseInt(this.leadSelecionado.score);
    }
    if (this.leadSelecionado.valorPotencial) {
      this.leadSelecionado.valorPotencial = parseFloat(this.leadSelecionado.valorPotencial);
    }

    // Se não tem o objeto crmEmpresa mas tem crmEmpresaId, buscar na lista
    if (!this.leadSelecionado.crmEmpresa && this.leadSelecionado.crmEmpresaId) {
      this.leadSelecionado.crmEmpresa = this.crmEmpresas.find(e => e.id == this.leadSelecionado.crmEmpresaId) || null;
    }
    
    console.log('Lead selecionado para edição:', this.leadSelecionado);
    this.modoEdicao = true;
  }

  cancelar(): void {
    this.leadSelecionado = {};
    this.modoEdicao = false;
  }

  salvar(): void {
    // Garantir que score seja número se preenchido
    if (this.leadSelecionado.score) {
      this.leadSelecionado.score = parseInt(this.leadSelecionado.score);
    }
    
    // Garantir que valorPotencial seja número se preenchido
    if (this.leadSelecionado.valorPotencial) {
      this.leadSelecionado.valorPotencial = parseFloat(this.leadSelecionado.valorPotencial);
    }

    // Preparar dados para envio - converter crmEmpresa para crmEmpresaId
    const dadosParaEnvio = {
      ...this.leadSelecionado,
      crmEmpresaId: this.leadSelecionado.crmEmpresa?.id || null
    };
    
    // Remover o objeto crmEmpresa completo para não enviar dados desnecessários
    delete dadosParaEnvio.crmEmpresa;
    
    console.log('Dados sendo enviados:', dadosParaEnvio);
    console.log('Modo edição - ID presente?', !!dadosParaEnvio.id);
    
    this.leadService.salveLead(dadosParaEnvio).then((resposta) => {
      console.log('Resposta do servidor:', resposta);
      alert('Lead salvo com sucesso!');
      this.cancelar();
      this.listar();
    }).catch((erro) => {
      console.error('Erro completo:', erro);
      console.error('Erro detalhado:', JSON.stringify(erro, null, 2));
      let mensagemErro = 'Erro desconhecido';
      
      if (erro.error && erro.error.mensagem) {
        mensagemErro = erro.error.mensagem;
      } else if (erro.message) {
        mensagemErro = erro.message;
      } else if (typeof erro === 'string') {
        mensagemErro = erro;
      }
      
      alert('Erro ao salvar lead: ' + mensagemErro);
    });
  }

  remover(id: number): void {
    if (!confirm('Deseja realmente remover este lead?')) return;
    this.leadService.removaLead(id).then(() => this.listar());
  }

  // Ação para avançar etapa
  avancarEtapa(lead: any): void {
    const etapasOrdem = ['Prospecção', 'Qualificação', 'Objeção', 'Fechamento', 'Ganho'];
    const indexAtual = etapasOrdem.indexOf(lead.etapa);
    
    if (indexAtual >= 0 && indexAtual < etapasOrdem.length - 1) {
      lead.etapa = etapasOrdem[indexAtual + 1];
      this.leadService.salveLead(lead).then(() => {
        this.listar();
      }).catch((erro) => {
        console.error('Erro ao avançar etapa:', erro);
        alert('Erro ao avançar etapa');
      });
    }
  }

  // Formatar telefone durante digitação
  formatarTelefone(): void {
    if (this.leadSelecionado.telefone) {
      let telefone = this.leadSelecionado.telefone.replace(/\D/g, '');
      if (telefone.length <= 11) {
        telefone = telefone.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
        telefone = telefone.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
      }
      this.leadSelecionado.telefone = telefone;
    }
  }

  // Formatar Instagram handle
  formatarInstagram(): void {
    if (this.leadSelecionado.instagramHandle) {
      let handle = this.leadSelecionado.instagramHandle.replace('@', '').trim();
      this.leadSelecionado.instagramHandle = handle;
    }
  }

  // Formatação de dados para exibição
  formatarValor(valor: number): string {
    if (!valor) return 'N/A';
    return valor.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  }

  formatarData(data: any): string {
    if (!data) return 'N/A';
    const dataObj = typeof data === 'string' ? new Date(data) : data;
    return dataObj.toLocaleDateString('pt-BR');
  }

  // Verificar se o lead está atrasado
  isAtrasado(lead: any): boolean {
    if (!lead.dataProximoFollowup) return false;
    const hoje = new Date();
    const followup = new Date(lead.dataProximoFollowup);
    return hoje > followup;
  }

  // Cor baseada no score
  getCorScore(score: number): string {
    if (!score) return '#6c757d';
    if (score >= 80) return '#28a745';
    if (score >= 50) return '#ffc107';
    return '#dc3545';
  }

  // Ícone baseado na etapa
  getIconeEtapa(etapa: string): string {
    const icones = {
      'Prospecção': 'fa-eye',
      'Qualificação': 'fa-search',
      'Objeção': 'fa-exclamation-triangle',
      'Fechamento': 'fa-handshake',
      'Ganho': 'fa-check-circle',
      'Perdido': 'fa-times-circle'
    };
    return icones[etapa] || 'fa-circle';
  }

  // Obter nome da empresa CRM pelo objeto
  getNomeCrmEmpresa(lead: any): string {
    if (lead.crmEmpresa && lead.crmEmpresa.nome) {
      return lead.crmEmpresa.nome;
    }
    if (lead.empresa) {
      return lead.empresa;
    }
    return 'N/A';
  }


  // Gerar URL do WhatsApp
  getWhatsappUrl(telefone: string): string {
    if (!telefone) return '#';
    const numeroLimpo = telefone.replace(/\D/g, '');
    return `https://wa.me/55${numeroLimpo}`;
  }

  // Gerar URL do Instagram
  getInstagramUrl(handle: string): string {
    if (!handle) return '#';
    return `https://instagram.com/${handle}`;
  }

  // Abrir lead em modal ou tela detalhada
  abrirLead(lead: any): void {
    // Por enquanto, abre o lead para edição
    // Futuramente pode abrir um modal de detalhes ou navegar para tela específica
    this.editar(lead);
  }

  // Abrir detalhes do lead navegando para /crm/home
  abrirDetalhesLead(lead: any): void {
    console.log('Abrindo detalhes do lead:', lead);
    // Navegar para /crm/home passando o lead como parâmetro
    this.router.navigate(['/crm/home'], { 
      queryParams: { 
        leadId: lead.id,
        nomeResponsavel: lead.nomeResponsavel,
        empresa: this.getNomeCrmEmpresa(lead),
        telefone: lead.telefone,
        etapa: lead.etapa,
        origem: lead.origem
      }
    });
  }

  buscarDadosInstagram(): void {
    if (!this.instagramUsername || !this.instagramUsername.trim()) {
      alert('Digite um username do Instagram');
      return;
    }

    const username = this.instagramUsername.replace('@', '').trim();
    this.buscandoInstagram = true;

    // Garantir que leadSelecionado existe e tem uma estrutura mínima
    if (!this.leadSelecionado) {
      this.leadSelecionado = {};
    }

    console.log('Chamando buscarDadosInstagram com:', { username });
    
    this.leadService.buscarDadosInstagram(username)
      .then((resposta) => {
        console.log('Resposta completa do serviço:', resposta);
        
        // O ServerService já extrai o campo 'data' da resposta
        const leadInstagram = resposta;
        console.log('Lead Instagram extraído:', leadInstagram);
        
        if (!leadInstagram) {
          throw new Error('Resposta do servidor não contém dados válidos');
        }
        
        // Preencher formulário com dados do Instagram
        this.leadSelecionado = {
          ...this.leadSelecionado,
          nomeResponsavel: leadInstagram.nomeResponsavel || leadInstagram.nome || '',
          empresa: leadInstagram.empresa || '',
          telefone: leadInstagram.telefone || '',
          instagramHandle: leadInstagram.instagramHandle || username,
          linkInsta: leadInstagram.linkInsta || '',
          bioInsta: leadInstagram.bioInsta || leadInstagram.bio || '',
          notas: leadInstagram.notas || '',
          origem: 'Instagram'
        };

        // Buscar e definir a empresa CRM correspondente
        if (leadInstagram.crmEmpresa) {
          // Buscar na lista de empresas carregadas
          const empresaEncontrada = this.crmEmpresas.find(e => e.id === leadInstagram.crmEmpresa.id);
          this.leadSelecionado.crmEmpresa = empresaEncontrada || leadInstagram.crmEmpresa;
        }

        console.log('Lead selecionado após preenchimento:', this.leadSelecionado);

        // Tentar detectar segmento baseado na bio
        if (this.leadSelecionado.bioInsta) {
          this.detectarSegmento(this.leadSelecionado.bioInsta);
        }

        alert('Dados do Instagram carregados com sucesso!');
        this.buscandoInstagram = false;
      })
      .catch((erro) => {
        console.error('Erro ao buscar dados do Instagram:', erro);
        alert('Erro ao buscar dados do Instagram: ' + (erro.message || 'Erro desconhecido'));
        this.buscandoInstagram = false;
      });
  }

  detectarSegmento(bio: string): void {
    if (!bio) return;
    
    const bioLower = bio.toLowerCase();
    
    if (bioLower.includes('pizza') || bioLower.includes('pizzaria')) {
      this.leadSelecionado.segmento = 'Pizzaria';
    } else if (bioLower.includes('hambur') || bioLower.includes('burger')) {
      this.leadSelecionado.segmento = 'Hamburgueria';
    } else if (bioLower.includes('lanche') || bioLower.includes('sanduí')) {
      this.leadSelecionado.segmento = 'Lanchonete';
    } else if (bioLower.includes('doce') || bioLower.includes('bolo') || bioLower.includes('confeit')) {
      this.leadSelecionado.segmento = 'Confeitaria';
    } else if (bioLower.includes('bar') || bioLower.includes('boteco') || bioLower.includes('cervej')) {
      this.leadSelecionado.segmento = 'Bar';
    } else if (bioLower.includes('food truck') || bioLower.includes('foodtruck')) {
      this.leadSelecionado.segmento = 'Food Truck';
    } else if (bioLower.includes('restaurante') || bioLower.includes('comida') || bioLower.includes('culinária')) {
      this.leadSelecionado.segmento = 'Restaurante';
    }
  }
} 