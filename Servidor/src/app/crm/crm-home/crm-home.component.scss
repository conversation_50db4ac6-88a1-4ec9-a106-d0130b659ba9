// Variáveis e cores
$primary: #3b7ddd;
$primary-dark: #2d6ec0;
$primary-light: #e8f2ff;
$accent: #2ecc71;
$accent-light: #e8f8ed;
$neutral: #4a6583;
$neutral-light: #edf1f7;
$warning: #f39c12;
$danger: #e74c3c;
$gray-light: #f8f9fa;
$gray-lighter: #f2f3f5;
$gray-border: #e0e0e0;
$gray-dark: #495057;
$text-dark: #344767;
$text-secondary: #666;
$radius: 8px;
$transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

.crm-home-container {
  padding: 0;
  max-width: 100%;
  height: 100vh;
  margin: 0;
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  display: flex;
  flex-direction: column;
  background-color: $gray-light;
}

// Cabeçalho principal compacto
.main-header {
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  color: white;
  padding: 12px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, $accent 0%, rgba(255,255,255,0.3) 50%, $accent 100%);
  }
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.main-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: -0.5px;
  color: white !important;
}

.subtitle {
  font-size: 13px;
  opacity: 0.9;
  font-weight: 400;
  margin-left: auto;
  color: white !important;
}

// Lead card no topo - Layout condensado
.lead-card-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
  padding: 16px 20px;
  border-bottom: 1px solid $gray-border;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
  min-height: 80px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, $primary 0%, $accent 50%, $primary 100%);
  }
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(59, 125, 221, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

// Layout condensado em linha única
.lead-condensed-layout {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 2;
  flex-wrap: wrap;
}

// Avatar e score compactos
.lead-avatar-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.lead-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 3px 8px rgba(59, 125, 221, 0.25);
  border: 2px solid white;
}

// Informações principais
.lead-main-info {
  flex: 1;
  min-width: 250px;
}

.lead-header-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.lead-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: $text-dark;
  line-height: 1.2;
}

.lead-details-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.detail-item {
  font-size: 12px;
  color: $text-secondary;
  display: flex;
  align-items: center;
  gap: 4px;
  
  i {
    color: $primary;
    opacity: 0.8;
    font-size: 11px;
  }
}

// Redes sociais compactas
.social-compact {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.social-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  text-decoration: none;
  transition: $transition;
  
  &.instagram {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(245, 87, 108, 0.4);
    }
  }
  
  &.linkedin {
    background-color: #0077b5;
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 119, 181, 0.4);
    }
  }
  
  &.website {
    background-color: $neutral;
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(74, 101, 131, 0.4);
    }
  }
}

// Informações extras
.extra-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: $text-secondary;
  
  i {
    color: $primary;
    width: 12px;
  }
  
  span {
    font-weight: 500;
  }
}


.stage-badge {
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  background-color: $gray-lighter;
  white-space: nowrap;
  
  &.prospecção {
    background-color: #e3f2fd;
    color: #1976d2;
  }
  
  &.qualificação {
    background-color: #e8f5e9;
    color: #388e3c;
  }
  
  &.objeção {
    background-color: #fff8e1;
    color: #f57f17;
  }
  
  &.fechamento {
    background-color: #e8eaf6;
    color: #3949ab;
  }
}

.score-badge {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 2px solid white;
}

// Ações rápidas compactas
.lead-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: $transition;
  font-size: 16px;
  
  &.phone {
    background-color: #e8f5e9;
    color: #27ae60;
    
    &:hover {
      background-color: #27ae60;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.email {
    background-color: #e3f2fd;
    color: #3498db;
    
    &:hover {
      background-color: #3498db;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.whatsapp {
    background-color: #e8f5e9;
    color: #25d366;
    
    &:hover {
      background-color: #25d366;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.calendar {
    background-color: #f3e5f5;
    color: #9b59b6;
    
    &:hover {
      background-color: #9b59b6;
      color: white;
      transform: translateY(-1px);
    }
  }
}

// Conteúdo principal
.crm-main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sugestoes-panel {
  flex: 1;
  overflow: hidden;
}

.actions-panel {
  padding: 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  
  .btn-primary {
    color: white;
    background-color: #3b7ddd;
    border: none;
    padding: 10px 18px;
    font-weight: 500;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #2d6ec0;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
    
    i {
      margin-right: 6px;
    }
  }
}

// Responsividade para layout condensado
@media (max-width: 1024px) {
  .lead-condensed-layout {
    gap: 12px;
  }
  
  .extra-info {
    display: none; // Oculta informações extras em telas menores
  }
}

@media (max-width: 768px) {
  .main-header {
    padding: 10px 16px;
  }
  
  .main-title {
    font-size: 18px;
  }
  
  .subtitle {
    font-size: 12px;
  }
  
  .lead-card-header {
    padding: 12px 16px;
  }
  
  .lead-condensed-layout {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .lead-main-info {
    min-width: 200px;
    order: 1;
  }
  
  .lead-avatar-section {
    order: 0;
  }
  
  .social-compact {
    order: 2;
  }
  
  .lead-actions {
    order: 3;
    width: 100%;
    justify-content: center;
  }
  
  .lead-details-row {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .header-content {
    gap: 8px;
  }
  
  .header-icon {
    font-size: 20px;
  }
  
  .main-title {
    font-size: 16px;
  }
  
  .subtitle {
    display: none; // Oculta subtitle em telas muito pequenas
  }
  
  .lead-name {
    font-size: 16px;
  }
  
  .detail-item {
    font-size: 11px;
  }
  
  .social-compact {
    gap: 6px;
  }
  
  .social-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}

// Estilos para carregamento
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  background-color: white;
  margin: 20px;
  border-radius: $radius;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

.loading-spinner {
  text-align: center;
  
  i {
    font-size: 32px;
    color: $primary;
    margin-bottom: 16px;
    display: block;
  }
  
  p {
    color: $text-secondary;
    margin: 0;
    font-size: 16px;
  }
}

// Estilos para página de download de dados
.download-data-page {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 50vh;
}

.download-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  padding: 32px;
  max-width: 450px;
  width: 100%;
  text-align: center;
  border: 1px solid $gray-border;
}

.download-header {
  margin-bottom: 24px;
  
  i {
    font-size: 48px;
    color: #e1306c;
    margin-bottom: 16px;
    display: block;
  }
  
  h2 {
    color: $text-dark;
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
  }
  
  p {
    color: $text-secondary;
    margin: 0;
    font-size: 16px;
    
    strong {
      color: $primary;
      font-weight: 600;
    }
  }
}

.download-content {
  p {
    color: $text-secondary;
    margin-bottom: 24px;
    font-size: 15px;
    line-height: 1.5;
  }
}

.btn-download-data {
  background: linear-gradient(135deg, #e1306c 0%, #fd5949 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: $transition;
  box-shadow: 0 4px 15px rgba(225, 48, 108, 0.3);
  margin-bottom: 16px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(225, 48, 108, 0.4);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  i {
    margin-right: 8px;
  }
}

.download-info {
  small {
    color: $text-secondary;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    
    i {
      color: $primary;
    }
  }
}

// Responsividade para download page
@media (max-width: 768px) {
  .download-card {
    padding: 24px;
    margin: 0 16px;
  }
  
  .download-header {
    i {
      font-size: 40px;
    }
    
    h2 {
      font-size: 20px;
    }
    
    p {
      font-size: 14px;
    }
  }
  
  .btn-download-data {
    padding: 12px 24px;
    font-size: 15px;
  }
}

// Estilos para a nova estrutura detalhada do lead
.lead-detailed-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  margin: 20px;
  overflow: hidden;
  border: 1px solid $gray-border;
}

// Cabeçalho do lead redesignado
.lead-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
  padding: 24px;
  border-bottom: 1px solid $gray-border;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, $primary 0%, $accent 50%, $primary 100%);
  }
}

.lead-avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.lead-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(59, 125, 221, 0.3);
  border: 3px solid white;
}

.score-badge {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  color: white;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  border: 3px solid white;
}

.lead-title-section {
  flex: 1;
  min-width: 0;
}

.lead-name {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: $text-dark;
  line-height: 1.2;
}

.lead-company {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: $text-secondary;
  font-weight: 500;
}

.lead-meta {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.stage-badge {
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: $gray-lighter;
  border: 1px solid $gray-border;
  
  &.prospecção {
    background-color: #e3f2fd;
    color: #1976d2;
    border-color: #90caf9;
  }
  
  &.qualificação {
    background-color: #e8f5e9;
    color: #388e3c;
    border-color: #a5d6a7;
  }
  
  &.objeção {
    background-color: #fff8e1;
    color: #f57f17;
    border-color: #ffcc02;
  }
  
  &.fechamento {
    background-color: #e8eaf6;
    color: #3949ab;
    border-color: #9fa8da;
  }
}

.origin-badge, .segment-badge {
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: $neutral-light;
  color: $neutral;
  border: 1px solid rgba(74, 101, 131, 0.3);
}

.lead-quick-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 25px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  transition: $transition;
  border: 2px solid transparent;
  
  &.phone-btn {
    background-color: #e8f5e9;
    color: #27ae60;
    border-color: rgba(39, 174, 96, 0.2);
    
    &:hover {
      background-color: #27ae60;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
    }
  }
  
  &.whatsapp-btn {
    background-color: #e8f5e9;
    color: #25d366;
    border-color: rgba(37, 211, 102, 0.2);
    
    &:hover {
      background-color: #25d366;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
    }
  }
  
  &.email-btn {
    background-color: #e3f2fd;
    color: #3498db;
    border-color: rgba(52, 152, 219, 0.2);
    
    &:hover {
      background-color: #3498db;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    }
  }
}

// Seções de informações
.lead-contact-section, .lead-business-section, .lead-notes-section {
  padding: 24px;
  border-bottom: 1px solid $gray-border;
  
  &:last-child {
    border-bottom: none;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: $text-dark;
  
  i {
    color: $primary;
    font-size: 20px;
  }
}

// Grid de contatos
.contact-grid, .business-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.contact-item, .business-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  label {
    font-size: 12px;
    font-weight: 600;
    color: $text-secondary;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.contact-value {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: $gray-light;
  border-radius: 8px;
  border: 1px solid $gray-border;
  
  i {
    color: $primary;
    font-size: 16px;
    width: 20px;
    text-align: center;
  }
  
  span {
    flex: 1;
    font-size: 14px;
    color: $text-dark;
    font-weight: 500;
  }
}

.contact-action {
  color: $primary;
  text-decoration: none;
  padding: 4px;
  border-radius: 4px;
  transition: $transition;
  
  &:hover {
    background-color: $primary;
    color: white;
    transform: scale(1.1);
  }
}

.website-link, .linkedin-link {
  color: $primary;
  text-decoration: none;
  font-weight: 500;
  flex: 1;
  
  &:hover {
    text-decoration: underline;
  }
}

.business-item {
  span {
    font-size: 14px;
    color: $text-dark;
    font-weight: 500;
    padding: 12px 16px;
    background-color: $gray-light;
    border-radius: 8px;
    border: 1px solid $gray-border;
  }
  
  &.follow-up-date span {
    background-color: $accent-light;
    color: $accent;
    border-color: rgba(46, 204, 113, 0.3);
    font-weight: 600;
  }
}

.interests-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 16px;
  background-color: $gray-light;
  border-radius: 8px;
  border: 1px solid $gray-border;
}

.interest-tag {
  padding: 4px 12px;
  background-color: $primary-light;
  color: $primary;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(59, 125, 221, 0.3);
}

// Seção de notas
.notes-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.note-item {
  label {
    font-size: 12px;
    font-weight: 600;
    color: $text-secondary;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    display: block;
  }
  
  p {
    margin: 0;
    padding: 16px;
    background-color: $gray-light;
    border-radius: 8px;
    border: 1px solid $gray-border;
    color: $text-dark;
    line-height: 1.5;
    font-size: 14px;
    
    &.instagram-notes {
      background-color: #e8f2ff;
      border-color: rgba(59, 125, 221, 0.3);
      white-space: pre-wrap;
      font-family: 'Courier New', monospace;
      font-size: 13px;
    }
  }
}

// Responsividade para estrutura detalhada
@media (max-width: 768px) {
  .lead-detailed-card {
    margin: 12px;
  }
  
  .lead-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .lead-avatar-section {
    width: 100%;
    justify-content: center;
  }
  
  .lead-title-section {
    text-align: center;
    width: 100%;
  }
  
  .lead-quick-actions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .contact-grid, .business-grid {
    grid-template-columns: 1fr;
  }
  
  .lead-contact-section, .lead-business-section, .lead-notes-section {
    padding: 16px;
  }
}
