import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ServerService } from '../../services/ServerService';

@Injectable()
export class CrmEmpresaService extends ServerService {
  constructor(protected http: HttpClient) {
    super(http);
  }

  private endpoint = '/crm/empresas';

  liste(params: any = {}): Promise<any> {
    return this.obtenha(this.endpoint, params);
  }

  selecione(id: number): Promise<any> {
    return this.obtenha(`${this.endpoint}/${id}`, {});
  }

  salveEmpresa(empresa: any): Promise<any> {
    return this.salve(this.endpoint, empresa);
  }

  removaEmpresa(id: number): Promise<any> {
    return this.remova(`${this.endpoint}/${id}`, {});
  }
} 