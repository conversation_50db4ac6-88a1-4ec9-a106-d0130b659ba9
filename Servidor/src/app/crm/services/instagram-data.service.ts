import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class InstagramDataService {
  private dadosInstagram: any = null;
  private username: string = '';

  /**
   * Armazena os dados do Instagram temporariamente
   */
  setDados(dados: any, username: string): void {
    debugger;
    this.dadosInstagram = dados;
    this.username = username;
    console.log('Dados do Instagram salvos no service:', { username, dados });
  }

  /**
   * Recupera os dados do Instagram armazenados
   */
  getDados(): { dados: any, username: string } | null {
    if (this.dadosInstagram) {
      return {
        dados: this.dadosInstagram,
        username: this.username
      };
    }
    return null;
  }

  /**
   * Limpa os dados armazenados
   */
  clearDados(): void {
    this.dadosInstagram = null;
    this.username = '';
    console.log('Dados do Instagram limpos do service');
  }

  /**
   * Verifica se há dados armazenados
   */
  hasDados(): boolean {
    return this.dadosInstagram !== null;
  }
}
