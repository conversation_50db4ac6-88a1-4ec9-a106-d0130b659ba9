import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ConversasService, ContextoConversa, DadosLead } from '../services/conversas.service';

@Component({
  selector: 'app-lead-info',
  templateUrl: './lead-info.component.html',
  styleUrls: ['./lead-info.component.scss']
})
export class LeadInfoComponent implements OnInit, OnDestroy {
  dadosLead: DadosLead = {};
  contextoConversa: ContextoConversa = { mensagens: [] };
  secaoAtiva: 'info' | 'interacoes' | 'notas' = 'info';
  
  private destroy$ = new Subject<void>();

  constructor(private conversasService: ConversasService) { }

  ngOnInit(): void {
    // Inscrever para receber atualizações do contexto de conversa
    this.conversasService.contextoConversa$
      .pipe(takeUntil(this.destroy$))
      .subscribe(contexto => {
        console.log('LeadInfo recebeu atualização de contexto:', contexto);
        this.contextoConversa = contexto;
        if (contexto.dadosLead) {
          this.dadosLead = contexto.dadosLead;
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Alterna entre as seções de informações do lead
   */
  alternarSecao(secao: 'info' | 'interacoes' | 'notas'): void {
    this.secaoAtiva = secao;
  }
  
  /**
   * Retorna uma cor baseada no score do lead
   */
  getCorDoScore(score: number | undefined): string {
    if (!score) return '#999'; // Cinza para score indefinido
    
    if (score >= 80) return '#2ecc71'; // Verde para score alto
    if (score >= 50) return '#f39c12'; // Amarelo para score médio
    return '#e74c3c'; // Vermelho para score baixo
  }
  
  /**
   * Formata o score para exibição
   */
  formatarScore(score: number | undefined): string {
    if (!score && score !== 0) return 'N/A';
    return `${score}%`;
  }
}