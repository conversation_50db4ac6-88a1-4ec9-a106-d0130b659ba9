<div class="novo-lead-container">
  <!-- Header -->
  <div class="header-section">
    <h2>Novo Lead</h2>
    <div class="breadcrumb">
      <span>CRM</span> / <span>Novo Lead</span>
    </div>
  </div>

  <!-- Card: Opções de Criação (quando não há dados) -->
  <div class="options-card" *ngIf="!dadosInstagram && !mostrarFormulario && username">
    <div class="options-content">
      <div class="option-item primary-option">
        <div class="option-icon">
          <i class="fab fa-instagram"></i>
        </div>
        <div class="option-details">
          <h4>Buscar Dados do Instagram</h4>
          <p>Colete automaticamente as informações do perfil para preencher o formulário</p>
        </div>
        <button class="btn btn-primary btn-lg" (click)="solicitarDadosInstagram()" [disabled]="carregando">
          <i class="fas fa-download" *ngIf="!carregando"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="carregando"></i>
          <span *ngIf="carregando">Buscando...</span>
          <span *ngIf="!carregando">Buscar Dados</span>
        </button>
      </div>

      <div class="option-divider">
        <span>ou</span>
      </div>

      <div class="option-item secondary-option">
        <div class="option-icon">
          <i class="fas fa-user-plus"></i>
        </div>
        <div class="option-details">
          <h4>Criar Lead Manualmente</h4>
          <p>Preencha o formulário manualmente sem buscar dados do Instagram</p>
        </div>
        <button class="btn btn-outline-primary btn-lg" (click)="mostrarFormularioManual()">
          <i class="fas fa-edit"></i>
          Criar Manual
        </button>
      </div>
    </div>

    <div class="info-footer">
      <i class="fas fa-info-circle"></i>
      <span>Para @{{username}} • Os dados do Instagram são públicos e coletados automaticamente</span>
    </div>
  </div>

  <!-- Card: Dados do Instagram -->
  <div class="card instagram-card" *ngIf="dadosInstagram">
    <div class="card-header">
      <i class="fab fa-instagram"></i>
      <h3>Dados do Instagram</h3>
    </div>
    <div class="card-body">
      <div class="instagram-profile">
        <div class="profile-image">
          <img [src]="getFotoPerfilInstagram()" [alt]="username" class="avatar">
        </div>
        <div class="profile-info">
          <h4>@{{dadosInstagram?.user?.username}}</h4>
          <p class="full-name">{{dadosInstagram?.user?.full_name}}</p>
          <p class="bio" *ngIf="dadosInstagram?.user?.biography">{{dadosInstagram?.user?.biography}}</p>
          
          <div class="stats">
            <div class="stat">
              <span class="count">{{getSeguidoresFormatado()}}</span>
              <span class="label">seguidores</span>
            </div>
            <div class="stat">
              <span class="count">{{dadosInstagram?.user?.edge_follow?.count || 0}}</span>
              <span class="label">seguindo</span>
            </div>
            <div class="stat">
              <span class="count">{{dadosInstagram?.user?.edge_owner_to_timeline_media?.count || 0}}</span>
              <span class="label">posts</span>
            </div>
          </div>

          <div class="account-type" *ngIf="dadosInstagram?.user?.is_business_account">
            <span class="badge badge-business">Conta Comercial</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Card: Formulário de Lead -->
  <div class="card formulario-card" *ngIf="dadosInstagram || mostrarFormulario">
    <div class="card-header">
      <i class="fas fa-user-plus"></i>
      <h3>Criar Novo Lead</h3>
    </div>
    <div class="card-body">
      
      <!-- Mensagem de erro -->
      <div class="alert alert-danger" *ngIf="erro">
        {{erro}}
      </div>

      <form #leadForm="ngForm">
        <!-- Seção: Informações Básicas -->
        <div class="form-section">
          <div class="section-title">
            <i class="fas fa-user"></i>
            Informações Básicas
          </div>
          
          <div class="row">
            <!-- Nome do Responsável -->
            <div class="col-md-6">
              <div class="form-group">
                <label for="nomeResponsavel">Nome do Responsável *</label>
                <input 
                  type="text" 
                  id="nomeResponsavel"
                  class="form-control" 
                  [(ngModel)]="lead.nomeResponsavel"
                  name="nomeResponsavel"
                  placeholder="Ex: João Silva"
                  required>
              </div>
            </div>

            <!-- Nome da Empresa -->
            <div class="col-md-6">
              <div class="form-group">
                <label for="empresa">Nome da Empresa *</label>
                <input 
                  type="text" 
                  id="empresa"
                  class="form-control" 
                  [(ngModel)]="lead.empresa"
                  name="empresa"
                  placeholder="Ex: Pizzaria Bella Vista"
                  required>
              </div>
            </div>
          </div>
        </div>

        <!-- Seção: Contato -->
        <div class="form-section">
          <div class="section-title">
            <i class="fas fa-address-card"></i>
            Contato e Redes Sociais
          </div>
          
          <div class="row">
            <!-- Username Instagram -->
            <div class="col-md-6">
              <div class="form-group">
                <label for="instagramHandle">Username Instagram *</label>
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text">@</span>
                  </div>
                  <input 
                    type="text" 
                    id="instagramHandle"
                    class="form-control" 
                    [(ngModel)]="lead.instagramHandle"
                    name="instagramHandle"
                    placeholder="restaurante_exemplo"
                    required>
                </div>
              </div>
            </div>

            <!-- Telefone -->
            <div class="col-md-6">
              <div class="form-group">
                <label for="telefone">Telefone</label>
                <input 
                  type="text" 
                  id="telefone"
                  class="form-control" 
                  [(ngModel)]="lead.telefone"
                  name="telefone"
                  placeholder="+55 11 99999-9999">
              </div>
            </div>
          </div>

          <!-- Bio Instagram -->
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label for="bioInsta">Bio do Instagram</label>
                <textarea 
                  id="bioInsta"
                  class="form-control" 
                  [(ngModel)]="lead.bioInsta"
                  name="bioInsta"
                  rows="3"
                  placeholder="Biografia extraída do perfil do Instagram..."></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- Seção: Classificação -->
        <div class="form-section">
          <div class="section-title">
            <i class="fas fa-tags"></i>
            Classificação e Funil
          </div>
          
          <div class="row">
            <!-- Etapa do Funil -->
            <div class="col-md-4">
              <div class="form-group">
                <label for="etapa">Etapa do Funil</label>
                <select 
                  id="etapa"
                  class="form-control" 
                  [(ngModel)]="lead.etapa"
                  name="etapa">
                  <option *ngFor="let etapa of etapas" [value]="etapa.valor">
                    {{etapa.texto}}
                  </option>
                </select>
              </div>
            </div>

            <!-- Origem -->
            <div class="col-md-4">
              <div class="form-group">
                <label for="origem">Origem</label>
                <select 
                  id="origem"
                  class="form-control" 
                  [(ngModel)]="lead.origem"
                  name="origem">
                  <option *ngFor="let origem of origens" [value]="origem.valor">
                    {{origem.texto}}
                  </option>
                </select>
              </div>
            </div>

            <!-- Segmento -->
            <div class="col-md-4">
              <div class="form-group">
                <label for="segmento">Segmento</label>
                <select 
                  id="segmento"
                  class="form-control" 
                  [(ngModel)]="lead.segmento"
                  name="segmento">
                  <option *ngFor="let segmento of segmentos" [value]="segmento.valor">
                    {{segmento.texto}}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Seção: Empresa CRM (se houver) -->
        <div class="form-section" *ngIf="crmEmpresas.length > 0">
          <div class="section-title">
            <i class="fas fa-building"></i>
            Empresa CRM
          </div>
          
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label for="crmEmpresaId">Empresa CRM (Opcional)</label>
                <select 
                  id="crmEmpresaId"
                  class="form-control" 
                  [(ngModel)]="lead.crmEmpresaId"
                  name="crmEmpresaId">
                  <option value="">Selecione uma empresa ou deixe em branco</option>
                  <option *ngFor="let empresa of crmEmpresas" [value]="empresa.id">
                    {{empresa.nome}}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Seção: Observações -->
        <div class="form-section">
          <div class="section-title">
            <i class="fas fa-sticky-note"></i>
            Observações Adicionais
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="biografia">Biografia/Descrição</label>
                <textarea 
                  id="biografia"
                  class="form-control" 
                  [(ngModel)]="lead.biografia"
                  name="biografia"
                  rows="4"
                  placeholder="Descrição geral sobre o lead e sua empresa..."></textarea>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="observacoes">Observações de Vendas</label>
                <textarea 
                  id="observacoes"
                  class="form-control" 
                  [(ngModel)]="lead.observacoes"
                  name="observacoes"
                  rows="4"
                  placeholder="Observações sobre o processo de vendas, necessidades específicas, histórico de contato, etc..."></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- Opções de Sincronização -->
        <div class="form-section">
          <div class="section-title">
            <i class="fas fa-sync-alt"></i>
            Sincronização
          </div>
          
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <div class="form-check">
                  <input 
                    type="checkbox" 
                    id="sincronizarBitrix"
                    class="form-check-input" 
                    [(ngModel)]="sincronizarBitrix"
                    name="sincronizarBitrix">
                  <label class="form-check-label" for="sincronizarBitrix">
                    <i class="fas fa-external-link-alt"></i>
                    Sincronizar automaticamente com Bitrix24
                  </label>
                  <small class="form-text text-muted">
                    Quando marcado, o lead será criado automaticamente no Bitrix24 após salvar
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Botões de ação -->
        <div class="form-actions">
          <button 
            type="button" 
            class="btn btn-secondary" 
            (click)="cancelar()"
            [disabled]="carregando">
            Cancelar
          </button>
          
          <button 
            type="button" 
            class="btn btn-primary" 
            (click)="salvarLead()"
            [disabled]="carregando || !leadForm.valid">
            <i class="fas fa-spinner fa-spin" *ngIf="carregando"></i>
            {{carregando ? 'Criando...' : 'Criar Lead'}}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>