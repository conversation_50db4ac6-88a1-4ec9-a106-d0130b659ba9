// Previne problemas de largura no iframe da extensão
:host {
  display: block;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

* {
  box-sizing: border-box;
}

.novo-lead-container {
  padding: 16px;
  max-width: 100%;
  width: 100%;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  min-width: 0;
}

.header-section {
  margin-bottom: 30px;

  h2 {
    color: #333;
    margin-bottom: 5px;
  }

  .breadcrumb {
    color: #666;
    font-size: 14px;
    
    span:last-child {
      font-weight: 500;
    }
  }
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;

  .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;

    i {
      color: #6c757d;
      font-size: 18px;
    }

    h3 {
      margin: 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .card-body {
    padding: 20px;
  }
}


// Card Instagram
.instagram-card {
  .card-header {
    background: #E1306C;
    border: none;
    color: white;
    
    i, h3 {
      color: white;
    }
  }
}

// Card de Opções Moderno
.options-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
  margin-bottom: 24px;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;

  .options-content {
    padding: 24px;
  }

  .option-item {
    display: flex;
    align-items: center;
    padding: 24px;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.2s ease;

    &.primary-option {
      background: #f8f9ff;
      border-color: #e7e9fc;
      
      &:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0,123,255,0.1);
      }
    }

    &.secondary-option {
      background: #fafafa;
      border-color: #e9ecef;
      
      &:hover {
        border-color: #6c757d;
        box-shadow: 0 2px 8px rgba(108,117,125,0.1);
      }
    }

    .option-icon {
      flex-shrink: 0;
      width: 56px;
      height: 56px;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      i {
        font-size: 24px;
        color: #007bff;
      }
    }

    .secondary-option .option-icon i {
      color: #6c757d;
    }

    .option-details {
      flex: 1;
      margin-right: 16px;

      h4 {
        margin: 0 0 6px 0;
        color: #212529;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #6c757d;
        font-size: 14px;
        line-height: 1.4;
      }
    }

    .btn {
      flex-shrink: 0;
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      white-space: nowrap;
      max-width: 160px;

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      &.btn-primary {
        background: #007bff;
        color: white;

        &:hover:not(:disabled) {
          background: #0056b3;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
      }

      &.btn-outline-primary {
        background: transparent;
        color: #007bff;
        border: 2px solid #007bff;

        &:hover:not(:disabled) {
          background: #007bff;
          color: white;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
      }

      &.btn-lg {
        padding: 12px 20px;
        font-size: 14px;
      }
    }
  }

  .option-divider {
    display: flex;
    align-items: center;
    margin: 20px 0;
    position: relative;

    &:before {
      content: '';
      flex: 1;
      height: 1px;
      background: #dee2e6;
    }

    &:after {
      content: '';
      flex: 1;
      height: 1px;
      background: #dee2e6;
    }

    span {
      padding: 0 16px;
      color: #6c757d;
      font-size: 14px;
      font-weight: 500;
      background: white;
    }
  }

  .info-footer {
    background: #f8f9fa;
    padding: 16px 32px;
    border-top: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 13px;

    i {
      color: #007bff;
    }
  }
}

.instagram-profile {
  display: flex;
  gap: 20px;
  align-items: flex-start;

  .profile-image {
    flex-shrink: 0;

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #e1306c;
    }
  }

  .profile-info {
    flex: 1;

    h4 {
      margin: 0 0 5px 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .full-name {
      margin: 0 0 10px 0;
      color: #666;
      font-weight: 500;
    }

    .bio {
      margin: 0 0 15px 0;
      color: #555;
      line-height: 1.4;
      font-size: 14px;
    }

    .stats {
      display: flex;
      gap: 20px;
      margin-bottom: 10px;

      .stat {
        text-align: center;

        .count {
          display: block;
          font-weight: 600;
          color: #333;
          font-size: 16px;
        }

        .label {
          display: block;
          color: #666;
          font-size: 12px;
          text-transform: uppercase;
        }
      }
    }

    .account-type {
      .badge {
        &.badge-business {
          background: #e1306c;
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }
}

// Formulário
.formulario-card {
  .form-group {
    margin-bottom: 24px;

    label {
      display: block;
      margin-bottom: 8px;
      color: #2c3e50;
      font-weight: 600;
      font-size: 14px;
      letter-spacing: 0.02em;
    }

    .form-control {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 15px;
      line-height: 1.4;
      transition: all 0.2s ease;
      background: #fff;
      box-sizing: border-box;
      min-height: 48px;
      height: auto;

      &:focus {
        outline: 0;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        background: #fafbfc;
      }

      &:hover:not(:focus) {
        border-color: #bdc6d0;
      }

      &:invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220,53,69,0.1);
      }

      &::placeholder {
        color: #8a95a6;
        font-style: italic;
      }
    }

    select.form-control {
      cursor: pointer;
      appearance: none;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 12px center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      min-height: 48px;
      line-height: 1.2;
      height: auto;
    }

    textarea.form-control {
      resize: vertical;
      min-height: 100px;
      font-family: inherit;
      line-height: 1.5;
    }
  }

  .input-group {
    display: flex;
    align-items: stretch;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    .input-group-prepend {
      display: flex;
      
      .input-group-text {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-right: 0;
        padding: 12px 16px;
        color: #495057;
        font-weight: 600;
        border-radius: 0;
        margin: 0;
        display: flex;
        align-items: center;
        font-size: 15px;
        line-height: 1.4;
        min-height: 48px;
        height: auto;
        box-sizing: border-box;
      }
    }

    .form-control {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-left: 0;
      margin: 0;
      flex: 1;
      
      &:focus {
        z-index: 3;
      }
    }
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
    width: 100%;
    box-sizing: border-box;

    .col-md-4, .col-md-6, .col-md-12 {
      padding: 0 8px;
      box-sizing: border-box;
      min-width: 0;
    }

    .col-md-4 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }

    .col-md-6 {
      flex: 0 0 50%;
      max-width: 50%;
    }

    .col-md-12 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  // Seções do formulário
  .form-section {
    margin-bottom: 32px;
    
    &:not(:last-child) {
      padding-bottom: 24px;
      border-bottom: 1px solid #e9ecef;
    }
    
    // Estilo especial para seção de sincronização
    &:has(.fa-sync-alt) {
      background: linear-gradient(135deg, #f8fafe 0%, #ffffff 100%);
      padding: 20px;
      border-radius: 8px;
      border: 1px solid rgba(59, 125, 221, 0.2);
      margin-bottom: 24px;
      
      .section-title {
        color: #3b7ddd;
        
        i {
          color: #3b7ddd;
        }
      }
      
      .form-check-label {
        color: #2c3e50;
        font-weight: 500;
        
        i {
          color: #3b7ddd;
          margin-right: 6px;
        }
      }
      
      .form-text {
        color: #6c757d;
        font-size: 12px;
        margin-top: 4px;
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #007bff;
        font-size: 14px;
      }
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;

  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.btn-secondary {
      background: #6c757d;
      color: white;

      &:hover:not(:disabled) {
        background: #5a6268;
      }
    }

    &.btn-primary {
      background: #007bff;
      color: white;

      &:hover:not(:disabled) {
        background: #0056b3;
      }
    }
  }
}

.alert {
  padding: 12px 16px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;

  &.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
  }
}

// Responsividade
@media (max-width: 768px) {
  .novo-lead-container {
    padding: 15px;
  }

  .options-card {
    margin-bottom: 20px;

    .options-content {
      padding: 20px;
    }

    .option-item {
      flex-direction: column;
      text-align: center;
      padding: 20px;
      gap: 16px;

      .option-icon {
        margin: 0 auto 12px auto;
      }

      .option-details {
        margin: 0;

        h4 {
          font-size: 16px;
        }

        p {
          font-size: 13px;
        }
      }

      .btn {
        width: 100%;
        max-width: none;
        justify-content: center;
      }
    }

    .info-footer {
      padding: 12px 20px;
      font-size: 12px;
    }
  }

  .instagram-profile {
    flex-direction: column;
    text-align: center;
    gap: 15px;

    .profile-image {
      align-self: center;

      .avatar {
        width: 70px;
        height: 70px;
      }
    }

    .stats {
      justify-content: center;
      gap: 15px;
    }
  }

  .formulario-card {
    .form-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 15px;
        margin-bottom: 16px;
      }
    }

    .row {
      margin: 0 -8px;

      .col-md-4, .col-md-6, .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 8px;
      }
    }

    .form-group {
      margin-bottom: 20px;

      label {
        font-size: 13px;
      }

      .form-control {
        padding: 10px 14px;
        font-size: 14px;
      }
    }

    .input-group {
      .input-group-text {
        padding: 10px 14px;
        font-size: 14px;
      }
    }
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;

    .btn {
      width: 100%;
      justify-content: center;
      font-size: 15px;
      padding: 12px 20px;
    }
  }
}

// Tablets (768px - 1024px)
@media (min-width: 768px) and (max-width: 1024px) {
  .formulario-card {
    .row {
      .col-md-4 {
        flex: 0 0 50%;
        max-width: 50%;

        &:last-child:nth-child(odd) {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }
  }
}