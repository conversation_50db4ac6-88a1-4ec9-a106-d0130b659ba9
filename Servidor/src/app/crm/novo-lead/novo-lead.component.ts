import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { InstagramDataService } from '../services/instagram-data.service';
import { LeadService } from '../services/lead.service';
import { CrmEmpresaService } from '../services/crm-empresa.service';

@Component({
  selector: 'app-novo-lead',
  templateUrl: './novo-lead.component.html',
  styleUrls: ['./novo-lead.component.scss']
})
export class NovoLeadComponent implements OnInit {

  // Dados do Instagram
  dadosInstagram: any = null;
  username: string = '';

  // Lead processado pela API dadosig2
  leadProcessadoAPI: any = null;

  // Estado do componente
  carregando = false;
  erro = '';
  mostrarFormulario = false;

  // Dados do formulário
  lead: any = {
    nomeResponsavel: '',
    empresa: '',
    telefone: '',
    instagramHandle: '',
    bioInsta: '',
    biografia: '',
    observacoes: '',
    origem: 'Instagram',
    etapa: 'Prospecção',
    segmento: 'Alimentação',
    crmEmpresaId: null
  };

  // Lista de empresas CRM para seleção
  crmEmpresas: any[] = [];

  // Opções para dropdowns
  etapas = [
    { valor: 'Prospecção', texto: 'Prospecção' },
    { valor: 'Qualificação', texto: 'Qualificação' },
    { valor: 'Objeção', texto: 'Objeção' },
    { valor: 'Fechamento', texto: 'Fechamento' },
    { valor: 'Ganho', texto: 'Ganho' },
    { valor: 'Perdido', texto: 'Perdido' }
  ];

  origens = [
    { valor: 'Instagram', texto: 'Instagram' },
    { valor: 'Site/Landing Page', texto: 'Site/Landing Page' },
    { valor: 'WhatsApp Direto', texto: 'WhatsApp Direto' },
    { valor: 'Indicação', texto: 'Indicação' },
    { valor: 'Evento/Feira', texto: 'Evento/Feira' },
    { valor: 'Outros', texto: 'Outros' }
  ];

  segmentos = [
    { valor: 'Alimentação', texto: 'Alimentação' },
    { valor: 'Varejo', texto: 'Varejo' },
    { valor: 'Serviços', texto: 'Serviços' },
    { valor: 'Saúde', texto: 'Saúde' },
    { valor: 'Educação', texto: 'Educação' },
    { valor: 'Outros', texto: 'Outros' }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private instagramDataService: InstagramDataService,
    private leadService: LeadService,
    private crmEmpresaService: CrmEmpresaService
  ) {}

  ngOnInit(): void {
    // Pega o username da URL
    this.route.queryParams.subscribe(params => {
      this.username = params['username'] || '';
      console.log('Username da URL:', this.username);
    });

    // Recupera dados do Instagram do service
    const dadosService = this.instagramDataService.getDados();
    if (dadosService) {
      this.dadosInstagram = dadosService.dados;
      this.username = dadosService.username;
      this.preencherFormularioComDadosInstagram();
    } else {
      console.warn('Nenhum dado do Instagram encontrado no service');

      // Se tem username, configura listener mas NÃO busca automaticamente
      if (this.username) {
        console.log('Username presente, aguardando ação do usuário para buscar dados');
        this.setupInstagramDataListener();
      } else {
        // Se não tem username, redireciona para home
        this.router.navigate(['/crm/home']);
      }
    }

    // Carrega lista de empresas CRM
    this.carregarCrmEmpresas();
  }

  /**
   * Preenche o formulário com dados do Instagram
   */
  private preencherFormularioComDadosInstagram(): void {
    if (!this.dadosInstagram || !this.dadosInstagram.user) {
      console.warn('Dados do Instagram não encontrados ou estrutura incorreta:', this.dadosInstagram);
      return;
    }

    const user = this.dadosInstagram.user;

    this.lead.nomeResponsavel = user.full_name || user.username || '';
    this.lead.empresa = user.full_name || user.username || '';
    this.lead.instagramHandle = user.username || '';
    this.lead.bioInsta = user.biography || '';
    this.lead.biografia = user.biography || '';
    this.lead.telefone = user.business_phone_number || '';

    console.log('Formulário preenchido com dados do Instagram:', this.lead);
  }

  /**
   * Preenche o formulário com dados do lead processado pela API
   * Agora recebe um objeto Lead completo com CrmEmpresa
   */
  private preencherFormularioComLeadProcessado(leadProcessado: any): void {
    if (!leadProcessado) {
      console.warn('Lead processado não encontrado:', leadProcessado);
      return;
    }

    console.log('Preenchendo formulário com lead processado (objeto Lead completo):', leadProcessado);

    // Armazena o lead processado para uso posterior
    this.leadProcessadoAPI = leadProcessado;

    // Preenche campos do formulário com dados do Lead
    this.lead.nomeResponsavel = leadProcessado.nomeResponsavel || '';
    this.lead.empresa = leadProcessado.empresa || '';
    this.lead.telefone = leadProcessado.telefone || '';
    this.lead.instagramHandle = leadProcessado.instagramHandle || '';
    this.lead.bioInsta = leadProcessado.bioInsta || '';
    this.lead.biografia = leadProcessado.bioInsta || '';
    this.lead.origem = leadProcessado.origem || 'Instagram';
    this.lead.etapa = leadProcessado.etapa || 'Prospecção';
    this.lead.score = leadProcessado.score || 0;

    // Mapear segmento baseado na categoria do negócio
    if (leadProcessado.instagramData?.businessCategory) {
      this.lead.segmento = this.mapearSegmento(leadProcessado.instagramData.businessCategory);
    } else {
      this.lead.segmento = 'Alimentação'; // padrão
    }

    // Define empresa CRM se disponível
    if (leadProcessado.crmEmpresaId) {
      this.lead.crmEmpresaId = leadProcessado.crmEmpresaId;
    }

    // Adiciona informações extras do Instagram nas notas se não estiverem presentes
    if (leadProcessado.notas) {
      this.lead.notas = leadProcessado.notas;
    }

    // Simula dados do Instagram para o template (se necessário)
    if (leadProcessado.instagramData) {
      this.dadosInstagram = {
        user: {
          username: leadProcessado.instagramHandle,
          full_name: leadProcessado.empresa,
          biography: leadProcessado.bioInsta,
          business_phone_number: leadProcessado.telefone,
          edge_followed_by: { count: leadProcessado.instagramData.followers },
          edge_follow: { count: leadProcessado.instagramData.following },
          is_business_account: leadProcessado.instagramData.accountType === 'Business',
          business_category_name: leadProcessado.instagramData.businessCategory,
          external_url: leadProcessado.instagramData.website,
          profile_pic_url: leadProcessado.avatarUrl
        }
      };
    }

    console.log('Formulário preenchido com lead processado:', this.lead);
    console.log('Dados do Instagram simulados para template:', this.dadosInstagram);
  }

  /**
   * Mapeia categoria de negócio para segmento
   */
  private mapearSegmento(categoria: string): string {
    if (!categoria) return 'Outros';

    const categoriaLower = categoria.toLowerCase();

    if (categoriaLower.includes('restaurante') ||
        categoriaLower.includes('comida') ||
        categoriaLower.includes('food') ||
        categoriaLower.includes('pizza') ||
        categoriaLower.includes('lanche') ||
        categoriaLower.includes('café') ||
        categoriaLower.includes('bar') ||
        categoriaLower.includes('japonês') ||
        categoriaLower.includes('delivery')) {
      return 'Alimentação';
    }

    if (categoriaLower.includes('loja') ||
        categoriaLower.includes('varejo') ||
        categoriaLower.includes('shop')) {
      return 'Varejo';
    }

    if (categoriaLower.includes('serviço') ||
        categoriaLower.includes('service')) {
      return 'Serviços';
    }

    if (categoriaLower.includes('saúde') ||
        categoriaLower.includes('health') ||
        categoriaLower.includes('médico') ||
        categoriaLower.includes('clínica')) {
      return 'Saúde';
    }

    if (categoriaLower.includes('educação') ||
        categoriaLower.includes('education') ||
        categoriaLower.includes('escola') ||
        categoriaLower.includes('curso')) {
      return 'Educação';
    }

    return 'Outros';
  }

  /**
   * Carrega lista de empresas CRM
   */
  private async carregarCrmEmpresas(): Promise<void> {
    try {
      const response = await this.crmEmpresaService.liste();
      if (response.sucesso) {
        this.crmEmpresas = response.dados || [];
      }
    } catch (error) {
      console.error('Erro ao carregar empresas CRM:', error);
    }
  }

  /**
   * Salva o novo lead
   */
  async salvarLead(): Promise<void> {
    if (!this.validarFormulario()) {
      return;
    }

    this.carregando = true;
    this.erro = '';

    try {
      // Se temos um lead processado pela API dadosig2, usa ele como base
      let leadParaSalvar: any;

      if (this.leadProcessadoAPI) {
        // Usa o lead processado pela API como base e aplica modificações do formulário
        leadParaSalvar = { ...this.leadProcessadoAPI };

        // Atualiza com dados modificados no formulário
        leadParaSalvar.nomeResponsavel = this.lead.nomeResponsavel;
        leadParaSalvar.empresa = this.lead.empresa;
        leadParaSalvar.telefone = this.lead.telefone;
        leadParaSalvar.instagramHandle = this.lead.instagramHandle;
        leadParaSalvar.bioInsta = this.lead.bioInsta;
        leadParaSalvar.origem = this.lead.origem;
        leadParaSalvar.etapa = this.lead.etapa;
        leadParaSalvar.segmento = this.lead.segmento;
        leadParaSalvar.crmEmpresaId = this.lead.crmEmpresaId;

        // Remove campos que não devem ser enviados na criação
        delete leadParaSalvar.id;
        delete leadParaSalvar.dataCriacao;
        delete leadParaSalvar.createdAt;
        delete leadParaSalvar.updatedAt;

      } else {
        // Fallback: cria lead do zero (modo manual)
        leadParaSalvar = { ...this.lead };

        // Adiciona dados do Instagram de forma estruturada (se disponível)
        if (this.dadosInstagram && this.dadosInstagram.user) {
          const user = this.dadosInstagram.user;

          // Dados básicos
          leadParaSalvar.avatarUrl = user.profile_pic_url;

          // Estrutura instagramData
          leadParaSalvar.instagramData = {
            bio: user.biography,
            followers: user.edge_followed_by?.count,
            following: user.edge_follow?.count,
            accountType: user.is_business_account ? 'Business' : 'Pessoal',
            businessCategory: user.business_category_name || user.category_name,
            location: user.business_address_json ? JSON.stringify(user.business_address_json) : undefined,
            website: user.external_url
          };
        }
      }

      console.log('Salvando lead:', leadParaSalvar);

      const leadCriado = await this.leadService.salveLead(leadParaSalvar);

      console.log('Lead criado com sucesso:', leadCriado);

      // Limpa dados do service
      this.instagramDataService.clearDados();

      // Redireciona para a home do lead criado
      this.router.navigate(['/crm/home', this.username]);
    } catch (error) {
      console.error('Erro ao salvar lead:', error);
      this.erro = 'Erro ao criar lead. Tente novamente.';
    } finally {
      this.carregando = false;
    }
  }

  /**
   * Valida o formulário
   */
  private validarFormulario(): boolean {
    if (!this.lead.nomeResponsavel) {
      this.erro = 'Nome do responsável é obrigatório';
      return false;
    }
    if (!this.lead.empresa) {
      this.erro = 'Nome da empresa é obrigatório';
      return false;
    }
    if (!this.lead.instagramHandle) {
      this.erro = 'Username do Instagram é obrigatório';
      return false;
    }
    return true;
  }

  /**
   * Cancela e volta para a home
   */
  cancelar(): void {
    this.instagramDataService.clearDados();
    this.router.navigate(['/crm/home', this.username]);
  }

  /**
   * Retorna a URL da foto do perfil do Instagram
   */
  getFotoPerfilInstagram(): string {
    if (this.dadosInstagram?.user?.profile_pic_url) {
      return this.dadosInstagram.user.profile_pic_url;
    }
    return '/assets/images/default-avatar.png'; // fallback
  }

  /**
   * Retorna o número de seguidores formatado
   */
  getSeguidoresFormatado(): string {
    const count = this.dadosInstagram?.user?.edge_followed_by?.count;
    if (!count) return '0';

    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  }

  /**
   * Solicita dados do Instagram via content script
   */
  solicitarDadosInstagram(): void {
    // Enviar mensagem para content script via iframe
    const message = {
      tipo: 'REQUEST_INSTAGRAM_DATA',
      username: this.username
    };

    // Comunicação via postMessage para parent window (content script)
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({tipo: "NOVA_MENSAGEM", text: message}, "*");
    }

    console.log('Solicitação de dados enviada para content script:', message);
  }

  /**
   * Configura listener para eventos do Instagram
   */
  private setupInstagramDataListener(): void {
    window.addEventListener('message', (event) => {
      // Verifica se é um evento de resposta do Instagram
      if (event.data && event.data.tipo === 'INSTAGRAM_DATA_RESPONSE') {
        console.log('Dados do Instagram recebidos:', event.data);
        this.processarDadosInstagram(event.data);
      }

      // Verifica se houve erro na busca
      if (event.data && event.data.tipo === 'INSTAGRAM_DATA_ERROR') {
        console.error('Erro ao buscar dados do Instagram:', event.data);
        this.processarErroInstagram(event.data);
      }
    });
  }

  /**
   * Processa os dados recebidos do Instagram
   */
  private async processarDadosInstagram(dadosInstagram: any): Promise<void> {
    console.log('Processando dados do Instagram para username:', dadosInstagram.username);

    try {
      // Primeiro, envia texto do Instagram para a API dadosig2 para processamento
      console.log('Enviando texto do Instagram para API dadosig2...');
      this.carregando = true;

      const leadProcessado = await this.leadService.enviarDadosInstagram(dadosInstagram.textoInsta, null, dadosInstagram.username);

      console.log('Lead processado pela API dadosig2:', leadProcessado);

      if (leadProcessado) {
        // ServerService já extraiu o 'dados', então leadProcessado é o objeto Lead completo
        console.log('Objeto Lead processado com sucesso:', leadProcessado);

        // Define username
        this.username = dadosInstagram.username;

        // Preenche formulário com dados do lead processado pela API
        // A função preencherFormularioComLeadProcessado agora simula dadosInstagram para o template
        this.preencherFormularioComLeadProcessado(leadProcessado);

        // Salva dados do Instagram no service (se necessário para outras funcionalidades)
        this.instagramDataService.setDados(this.dadosInstagram, this.username);

        console.log('Lead processado e formulário preenchido:', this.lead);
        console.log('Dados simulados para template:', this.dadosInstagram);
      } else {
        console.error('Erro: resposta vazia da API dadosig2');
        this.erro = 'Erro ao processar dados do Instagram na API.';
      }
    } catch (error) {
      console.error('Erro ao enviar dados para API dadosig2:', error);
      this.erro = 'Erro ao processar dados do Instagram. Tente novamente.';
    } finally {
      this.carregando = false;
    }
  }

  /**
   * Envia dados do Instagram para a API (caso necessário futuramente)
   */
  private async enviarDadosInstagramParaAPI(dadosInstagram: any): Promise<void> {
    try {
      console.log('Enviando dados do Instagram para API:', dadosInstagram.data);

      const response = await this.leadService.enviarDadosInstagram(dadosInstagram.data, null, dadosInstagram.username);

      if (response.sucesso) {
        console.log('Lead criado/atualizado com sucesso:', response.dados);
      } else {
        console.error('Erro na resposta da API:', response.erro);
      }
    } catch (error) {
      console.error('Erro ao enviar dados do Instagram para API:', error);
    }
  }

  /**
   * Processa erros na busca de dados do Instagram
   */
  private processarErroInstagram(errorData: any): void {
    console.error('Erro ao buscar dados do Instagram:', errorData.error);
    this.erro = 'Erro ao buscar dados do Instagram. Tente novamente.';
  }

  /**
   * Exibe o formulário para criação manual de lead
   */
  mostrarFormularioManual(): void {
    this.mostrarFormulario = true;
    console.log('Formulário manual ativado');
  }

}
