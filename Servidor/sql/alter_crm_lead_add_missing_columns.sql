-- Script para adicionar colunas faltantes na tabela crm_lead
-- Executar este script para corrigir o mapeamento entre o domain model e o banco de dados

-- Adicionar coluna empresa (nome da empresa do lead)
ALTER TABLE crm_lead ADD COLUMN empresa VARCHAR(255) NULL COMMENT 'Nome da empresa do lead';

-- <PERSON><PERSON><PERSON><PERSON> coluna link_insta (link externo da bio do Instagram)  
ALTER TABLE crm_lead ADD COLUMN link_insta VARCHAR(512) NULL COMMENT 'URL/link externo da bio do Instagram';

-- Atualizar registros existentes para preencher a coluna empresa com um valor padrão
-- baseado no nome do responsável (temporário até que os dados sejam atualizados corretamente)
UPDATE crm_lead SET empresa = CONCAT(nome_responsavel, ' - Empresa') WHERE empresa IS NULL;
